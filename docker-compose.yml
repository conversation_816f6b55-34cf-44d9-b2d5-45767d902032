version: '3.8'

services:
  # db:
  #   image: postgres:15
  #   environment:
  #     POSTGRES_USER: postgres
  #     POSTGRES_PASSWORD: postgres
  #     POSTGRES_DB: icp_tools
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./BuyBot/src/db/schema.sql:/docker-entrypoint-initdb.d/schema.sql
  #   ports:
  #     - "5432:5432"
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U postgres"]
  #     interval: 5s
  #     timeout: 5s
  #     retries: 5
  #   networks:
  #     - app-network
  #   logging:
  #     driver: "json-file"
  #     options:
  #       max-size: "10m"
  #       max-file: "3"

  buybot:
    build:
      context: ./BuyBot
      dockerfile: Dockerfile
    environment:
      TELEGRAM_BOT_TOKEN: "**********************************************"
      FETCHER_HOST: fetcher
      PGHOST: ${PGHOST}
      PGPORT: ${PGPORT}
      PGUSER: postgres
      PGPASSWORD: ${PGPASSWORD}
      PGDATABASE: ${PGDATABASE}
      TRENDING_CHANNEL_ID: "-1002350080963"
      TRENDING_MESSAGE_ID: "4"
      NODE_ENV: production
      LOG_LEVEL: debug
      DEBUG: "*"
    volumes:
      - ./logs/buybot:/app/logs
    # depends_on:
    #   fetcher:
    #     # condition: service_healthy
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        
  fetcher:
    build:
      context: ./Fetcher
      dockerfile: Dockerfile
    environment:
      PGHOST: ${PGHOST}
      PGPORT: ${PGPORT}
      PGUSER: postgres
      PGPASSWORD: ${PGPASSWORD}
      PGDATABASE: ${PGDATABASE}
      NODE_ENV: production
      LOG_LEVEL: debug
      DEBUG: "*"
    ports:
      - "3000:3000"
      - "2137:2137"  # WebSocket port
    expose:
      - "2137"  # Expose WebSocket port to other services
    volumes:
      - ./logs/fetcher:/app/logs
    networks:
      - app-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  app-network:
    driver: bridge
    name: icp-tools-network

volumes:
  postgres_data:
  logs:
