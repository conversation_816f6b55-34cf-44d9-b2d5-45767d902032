Operation failed (attempt 1/3): Cannot use a pool after calling end on the pool
Non-retryable error: Cannot use a pool after calling end on the pool
Failed to get pool addresses for ryjl3-tyaaa-aaaaa-aaaba-cai: Error: Cannot use a pool after calling end on the pool
    at /home/<USER>/repos/icp-tools/Fetcher/node_modules/pg-pool/index.js:45:11
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/home/<USER>/repos/icp-tools/Fetcher/src/services/database/db.ts:170:22)
    at async withRetry (/home/<USER>/repos/icp-tools/Fetcher/src/services/database/db.ts:41:14)
Operation failed (attempt 1/3): Cannot use a pool after calling end on the pool
Non-retryable error: Cannot use a pool after calling end on the pool
Failed to fetch token info for ryjl3-tyaaa-aaaaa-aaaba-cai: Error: Cannot use a pool after calling end on the pool
    at /home/<USER>/repos/icp-tools/Fetcher/node_modules/pg-pool/index.js:45:11
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/home/<USER>/repos/icp-tools/Fetcher/src/services/database/db.ts:240:25)
    at async withRetry (/home/<USER>/repos/icp-tools/Fetcher/src/services/database/db.ts:41:14)
Error processing transaction: TypeError: Cannot read properties of undefined (reading 'decimals')
    at ICPService.processTransactions (/home/<USER>/repos/icp-tools/Fetcher/src/services/icp/icp.ts:129:77)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ICPService.fetchTransactions (/home/<USER>/repos/icp-tools/Fetcher/src/services/icp/icp.ts:217:9)
    at async Timeout.fetchWithLock (/home/<USER>/repos/icp-tools/Fetcher/src/services/icp/icp.ts:469:9) {



implement retrieving holder balance from canister.
https://dashboard.internetcomputer.org/canister/h7uwa-hyaaa-aaaam-qbgvq-cai


_ICP","spentDollars":0.4737356087540538,"marketcap":576282.4776325751,"dex":"KongSwap","type":"NewBuy","timestamp":"2025-07-07 20:21"}                                                                           buybot-1   | 2025-07-07T20:21:50.239Z info: Received message from Fetcher                                                                                                                                        buybot-1   | {                                                                                               t       e        %             ty     6               d          0     2                            buybot-1   |   "context": "WebSocket"
buybot-1   | }
buybot-1   | Received message: {"spentToken":{"address":"ryjl3-tyaaa-aaaaa-aaaba-cai","symbol":"ICP","name":"Internet Computer","amount":0.1,"priceUSD":"4.737356087540538","pricePairToken":41.6617138},"gotToken":{"address":"rh2pm-ryaaa-aaaan-qeniq-cai","symbol":"EXE","name":"Windoge98","amount":4.16617138,"priceUSD":0.059216951094256724,"pricePairToken":0.024002853190355312},"pairAddress":"KONG_EXE_ICP","spentDollars":0.4737356087540538,"marketcap":576282.4776325751,"dex":"KongSwap","type":"NewBuy","timestamp":"2025-07-07 20:21"}                                                                                            buybot-1   | 2025-07-07T20:21:50.242Z info: Processing buy message
buybot-1   | {
buybot-1   |   "context": "Buy Message",
buybot-1   |   "token": "Windoge98",
buybot-1   |   "groups": 1,
buybot-1   |   "amount": 0.1,
buybot-1   |   "spentAmountDollars": 0.4737356087540538                                                                                                                                                          buybot-1   | }                                                                                                                                                                                                   buybot-1   | 2025-07-07T20:21:50.250Z info: **__🚨 Windoge98 New Buy!🚨__**                                                                                                                                      buybot-1   |                                                                                                                                                                                                     buybot-1   | 🚀                                                                                                                                                                                                  buybot-1   |                                                                                                                                                                                                     buybot-1   | **💰Spent: 0.1000 ICP \[$0.47]**                                                                                                                                                                    buybot-1   | **🧳Bought: 4.166 $EXE**                                                                                                                                                                            buybot-1   | **💵Price: $0.05922**                                                                                                                                                                               buybot-1   | **📊Marketcap: $576,282**                                                                                                                                                                           buybot-1   | 🕐2025-07-07 20:21 UTC                                                                                                                                                                              buybot-1   |                                                                                                                                                                                                     buybot-1   | **📈[DexScreener](https://dexscreener.com/icp/rh2pm-ryaaa-aaaan-qeniq-cai) | [DexTools](https://www.dextools.io/app/en/icp/pair-explorer/KONG_EXE_ICP) | [ICPSwap](https://app.icpswap.com/info-tokens/details/rh2pm-ryaaa-aaaan-qeniq-cai)**                                                                                                                                                                        buybot-1   |                                                                                                                                                                                                     buybot-1   | {                                                                                                                                                                                                   fetcher-1  | Buy message sent to client                                                                                                                                                                          buybot-1   |   "context": "Buy Message"                                                                                                                                                                          buybot-1   | }                                                                                                                                                                                                   buybot-1   | 2025-07-07T20:21:50.251Z error: {                                                                                                                                                                   buybot-1   |   "context": "Failed to send buy message to group",                                                                                                                                                 buybot-1   |   "error": {                                                                                                                                                                                        buybot-1   |     "message": "Cannot read properties of undefined (reading 'address')",                                                                                                                           buybot-1   |     "name": "TypeError",                                                                                                                                                                            buybot-1   |     "stack": "TypeError: Cannot read properties of undefined (reading 'address')\n    at WebSocket.<anonymous> (/app/dist/services/websocket.js:95:100)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"    