name = "openchat-buy-bot"
main = "dist/worker.js"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "openchat-buy-bot-prod"

[env.staging]
name = "openchat-buy-bot-staging"

# Environment variables (set these using wrangler secret put)
# IDENTITY_PRIVATE - OpenChat bot private key
# OC_PUBLIC - OpenChat public key
# IC_HOST - IC host URL
# STORAGE_INDEX_CANISTER - Storage canister ID
# WEBSOCKET_URL - Fetcher WebSocket URL

# KV namespace for persistent storage (optional upgrade from in-memory)
# [[kv_namespaces]]
# binding = "BOT_STORAGE"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"

# Durable Objects for WebSocket connections (optional advanced feature)
# [[durable_objects.bindings]]
# name = "WEBSOCKET_MANAGER"
# class_name = "WebSocketManager"

# Custom domains (configure after deployment)
# [[env.production.routes]]
# pattern = "bot.yourdomain.com/*"
# zone_name = "yourdomain.com"
