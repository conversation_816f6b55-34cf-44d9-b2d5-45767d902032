{"name": "@open-ic/bot-example", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "build:worker": "tsc && cp dist/worker.js dist/worker.js.bak", "start": "node dist/server.js", "dev": "ts-node src/server.ts", "dev:worker": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env staging", "deploy:production": "wrangler deploy --env production", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@dfinity/identity": "^2.4.1", "@dfinity/identity-secp256k1": "^2.4.1", "@dfinity/principal": "^2.4.1", "@open-ic/openchat-botclient-ts": "./lib", "@types/ws": "^8.18.1", "axios": "^1.10.0", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.1", "express-rate-limit": "^7.5.0", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "openai": "^4.73.0", "sharp": "^0.33.5", "websocat": "^1.1.0", "ws": "^8.18.3"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250705.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/mime-types": "^2.1.4", "@types/node": "^22.10.5", "ts-node": "^10.9.2", "typescript": "^5.7.3", "wrangler": "^4.23.0"}}