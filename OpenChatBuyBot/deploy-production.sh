#!/bin/bash

# OpenChat Buy Bot Production Deployment Script with PM2
echo "🚀 OpenChat Buy Bot Production Deployment"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    print_warning "PM2 not found. Installing PM2..."
    npm install -g pm2
    if [ $? -ne 0 ]; then
        print_error "Failed to install PM2"
        exit 1
    fi
    print_status "PM2 installed successfully"
fi

# Check if cloudflared exists
CLOUDFLARED_PATH="../cloudflared"
if [ ! -f "$CLOUDFLARED_PATH" ]; then
    print_error "cloudflared not found at $CLOUDFLARED_PATH"
    print_info "Please ensure cloudflared binary is available at the specified path"
    exit 1
fi

print_status "Found cloudflared binary"

# Check if .env file exists
if [ ! -f .env ]; then
    print_error ".env file not found!"
    echo "📋 Please copy .env.example to .env and fill in your credentials"
    echo "   cp .env.example .env"
    echo "   nano .env  # Edit with your values"
    exit 1
fi

# Load environment variables
source .env

# Check required variables
echo "🔍 Checking environment variables..."

if [ -z "$IDENTITY_PRIVATE" ]; then
    print_error "IDENTITY_PRIVATE not set"
    exit 1
fi

if [ -z "$OC_PUBLIC" ]; then
    print_error "OC_PUBLIC not set"
    exit 1
fi

if [ -z "$IC_HOST" ]; then
    print_error "IC_HOST not set"
    exit 1
fi

if [ -z "$STORAGE_INDEX_CANISTER" ]; then
    print_error "STORAGE_INDEX_CANISTER not set"
    exit 1
fi

print_status "All required environment variables are set"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install --production
    if [ $? -ne 0 ]; then
        print_error "Failed to install dependencies"
        exit 1
    fi
    print_status "Dependencies installed"
fi

# Build the project
echo "🔨 Building project..."
npm run build

if [ $? -ne 0 ]; then
    print_error "Build failed"
    exit 1
fi

print_status "Build successful"

# Stop existing PM2 processes
echo "🧹 Stopping existing PM2 processes..."
pm2 stop openchat-buy-bot 2>/dev/null || true
pm2 stop cloudflare-tunnel 2>/dev/null || true
pm2 delete openchat-buy-bot 2>/dev/null || true
pm2 delete cloudflare-tunnel 2>/dev/null || true

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'openchat-buy-bot',
      script: 'dist/server.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      error_file: './logs/bot-error.log',
      out_file: './logs/bot-out.log',
      log_file: './logs/bot-combined.log',
      time: true
    },
    {
      name: 'cloudflare-tunnel',
      script: '$CLOUDFLARED_PATH',
      args: 'tunnel --url http://localhost:3002',
      instances: 1,
      autorestart: true,
      watch: false,
      error_file: './logs/tunnel-error.log',
      out_file: './logs/tunnel-out.log',
      log_file: './logs/tunnel-combined.log',
      time: true
    }
  ]
};
EOF

# Create logs directory
mkdir -p logs

# Start applications with PM2
echo "🚀 Starting OpenChat Buy Bot with PM2..."
pm2 start ecosystem.config.js

if [ $? -ne 0 ]; then
    print_error "Failed to start applications with PM2"
    exit 1
fi

print_status "Applications started with PM2"

# Wait for services to start
sleep 10

# Test bot health
echo "🧪 Testing bot health..."
if curl -f http://localhost:3002/health > /dev/null 2>&1; then
    print_status "Bot health check passed"
else
    print_error "Bot health check failed"
    pm2 logs openchat-buy-bot --lines 20
    exit 1
fi

# Get tunnel URL from PM2 logs
echo "🌐 Extracting tunnel URL..."
sleep 5
TUNNEL_URL=$(pm2 logs cloudflare-tunnel --lines 50 --nostream | grep -o 'https://[^[:space:]]*\.trycloudflare\.com' | tail -1)

if [ -z "$TUNNEL_URL" ]; then
    print_warning "Could not automatically detect tunnel URL"
    print_info "Check PM2 logs for tunnel URL: pm2 logs cloudflare-tunnel"
    TUNNEL_URL="https://your-tunnel-url.trycloudflare.com"
fi

# Test tunnel endpoint
echo "🧪 Testing tunnel connectivity..."
sleep 5
if curl -f "$TUNNEL_URL/health" > /dev/null 2>&1; then
    print_status "Tunnel connectivity test passed"
else
    print_warning "Tunnel connectivity test failed (tunnel might still be starting)"
fi

# Save PM2 configuration
pm2 save

echo ""
echo "🎉 OpenChat Buy Bot Production Deployment Complete!"
echo "=================================================="
echo ""
print_status "Bot is running with PM2 on: http://localhost:3002"
print_status "Cloudflare tunnel URL: $TUNNEL_URL"
echo ""
echo "📊 Available endpoints:"
echo "   - GET  $TUNNEL_URL/health (health check)"
echo "   - GET  $TUNNEL_URL/bot_definition (bot schema)"
echo "   - POST $TUNNEL_URL/execute_command (OpenChat commands)"
echo ""
echo "🔧 PM2 Management Commands:"
echo "   - pm2 status                    # Check status"
echo "   - pm2 logs openchat-buy-bot     # View bot logs"
echo "   - pm2 logs cloudflare-tunnel    # View tunnel logs"
echo "   - pm2 restart openchat-buy-bot  # Restart bot"
echo "   - pm2 stop openchat-buy-bot     # Stop bot"
echo "   - pm2 monit                     # Monitor processes"
echo ""
echo "📋 Next steps:"
echo "1. Copy the tunnel URL above"
echo "2. Go to OpenChat and register your bot"
echo "3. Use the tunnel URL as your bot's endpoint"
echo "4. Test with /register command in your OpenChat group"
echo ""
echo "🛑 To stop the deployment:"
echo "   pm2 stop all"
echo "   or run: ./stop-production.sh"
echo ""

# Create production stop script
cat > stop-production.sh << 'EOF'
#!/bin/bash
echo "🛑 Stopping OpenChat Buy Bot production deployment..."

# Stop PM2 processes
pm2 stop openchat-buy-bot cloudflare-tunnel

echo "✅ Production deployment stopped"
echo "ℹ️  To completely remove: pm2 delete openchat-buy-bot cloudflare-tunnel"
EOF

chmod +x stop-production.sh

print_status "Production deployment script created: stop-production.sh"
print_info "The bot will automatically restart if it crashes"
print_info "Logs are saved in ./logs/ directory"
