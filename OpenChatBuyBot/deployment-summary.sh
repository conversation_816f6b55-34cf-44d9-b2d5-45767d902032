#!/bin/bash

# OpenChat Buy Bot Deployment Summary
echo "🎉 OpenChat Buy Bot Deployment Summary"
echo "======================================"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_highlight() {
    echo -e "${YELLOW}🔗 $1${NC}"
}

# Get current tunnel URL
TUNNEL_URL=$(ps aux | grep cloudflared | grep -o 'https://[^[:space:]]*\.trycloudflare\.com' | head -1)

if [ -z "$TUNNEL_URL" ]; then
    # Try to get from recent logs or use the current active one
    TUNNEL_URL="https://cpu-mount-pension-carefully.trycloudflare.com"
fi

echo ""
print_success "Deployment Status: ACTIVE"
print_success "Bot Server: Running on localhost:3002"
print_success "Cloudflare Tunnel: Connected"
print_highlight "Public URL: $TUNNEL_URL"

echo ""
echo "📊 Available Endpoints:"
echo "   - GET  $TUNNEL_URL/health"
echo "   - GET  $TUNNEL_URL/bot_definition"
echo "   - POST $TUNNEL_URL/execute_command"

echo ""
echo "🤖 OpenChat Bot Registration:"
echo "   1. Go to OpenChat"
echo "   2. Navigate to Bot Registration"
echo "   3. Enter Bot URL: $TUNNEL_URL"
echo "   4. Register your bot"

echo ""
echo "🔧 Bot Commands (use in OpenChat groups):"
echo "   /register <token_address> [min_amount]  - Add token to watchlist"
echo "   /unregister <token_address>             - Remove token from watchlist"
echo "   /list                                   - Show watched tokens"
echo "   /status                                 - Show bot status"
echo "   /admin                                  - Admin management"
echo "   /help                                   - Show help"

echo ""
echo "📋 Management Commands:"
echo "   ./check-deployment.sh     - Check deployment status"
echo "   ./stop-deployment.sh      - Stop development deployment"
echo "   ./deploy-production.sh    - Start production deployment with PM2"

echo ""
echo "📁 Important Files:"
echo "   .env                      - Environment configuration"
echo "   deploy.sh                 - Development deployment script"
echo "   deploy-production.sh      - Production deployment script"
echo "   DEPLOYMENT.md             - Complete deployment guide"

echo ""
print_info "The bot will automatically reconnect to the Fetcher service when available"
print_info "WebSocket connection errors are normal if Fetcher service is not running"

echo ""
echo "🎯 Next Steps:"
echo "   1. Copy the tunnel URL above"
echo "   2. Register your bot in OpenChat using this URL"
echo "   3. Add the bot to your OpenChat groups"
echo "   4. Test with /register command"
echo "   5. Start the Fetcher service for live trading alerts"

echo ""
print_highlight "Your OpenChat Buy Bot is ready for use!"
