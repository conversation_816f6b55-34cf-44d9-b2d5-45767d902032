# OpenChat Buy Bot Environment Variables
# Copy this file to .env and fill in your actual values

# OpenChat Bot Credentials (GET THESE FROM OPENCHAT)
IDENTITY_PRIVATE="-----BEGIN EC PRIVATE KEY-----
YOUR_PRIVATE_KEY_CONTENT_HERE
-----END EC PRIVATE KEY-----"

OC_PUBLIC="-----BEGIN PUBLIC KEY-----
OPENCHAT_PUBLIC_KEY_CONTENT_HERE
-----END PUBLIC KEY-----"

# IC Network Configuration (PROVIDED)
IC_HOST="https://icp-api.io"
STORAGE_INDEX_CANISTER="rturd-qaaaa-aaaaf-aabaq-cai"

# WebSocket Configuration (DEPENDS ON YOUR FETCHER SERVICE)
WEBSOCKET_URL="ws://localhost:2137"
# For production, use: wss://your-fetcher-domain.com:2137

# Server Configuration (OPTIONAL)
PORT=3000
