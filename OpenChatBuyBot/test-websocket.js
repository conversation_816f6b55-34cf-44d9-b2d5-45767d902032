const WebSocket = require('ws');

// Test message data
const testBuyMessage = {
  "spentToken": {
    "address": "ryjl3-tyaaa-aaaaa-aaaba-cai",
    "symbol": "ICP",
    "name": "ICP",
    "amount": 0.25,
    "priceUSD": 4.82421065894822,
    "pricePairToken": 10.44671008
  },
  "gotToken": {
    "address": "vpyot-zqaaa-aaaaa-qavaq-cai",
    "symbol": "FUNNAI",
    "name": "FUNNAI",
    "amount": 2.61167752,
    "priceUSD": 0.46179233672657066,
    "pricePairToken": 0.09572391617476571
  },
  "pairAddress": "c5u7l-rqaaa-aaaar-qbqta-cai",
  "spentDollars": 1.206052664737055,
  "holderIncrease": "",
  "holderWallet": "ehq2s-mlmrx-xvogi-adg5n-dowp3-qasvb-nvdjl-ijj3m-e6ijc-pmrmb-dqe",
  "marketcap": 131210.40168393732,
  "dex": "ICP Swap",
  "type": "NewBuy",
  "timestamp": "2025-07-08 20:23",
  "holderPercentageOwned": 0.000038890591364678027
};

// Additional test messages for variety
const testMessages = [
  {
    ...testBuyMessage,
    "spentDollars": 150.50,
    "gotToken": {
      ...testBuyMessage.gotToken,
      "amount": 325.75
    },
    "timestamp": new Date().toISOString().slice(0, 16).replace('T', ' ')
  },
  {
    ...testBuyMessage,
    "spentDollars": 2500.00,
    "gotToken": {
      ...testBuyMessage.gotToken,
      "amount": 5412.33
    },
    "holderIncrease": "+15.2%",
    "timestamp": new Date().toISOString().slice(0, 16).replace('T', ' ')
  },
  {
    ...testBuyMessage,
    "spentDollars": 50.25,
    "gotToken": {
      ...testBuyMessage.gotToken,
      "amount": 108.91
    },
    "holderIncrease": "New Holder!",
    "timestamp": new Date().toISOString().slice(0, 16).replace('T', ' ')
  }
];

function sendTestMessage(ws, messageIndex = 0) {
  const message = testMessages[messageIndex] || testBuyMessage;

  console.log(`📤 Sending test buy message #${messageIndex + 1}:`);
  console.log(`   Token: ${message.gotToken.symbol}`);
  console.log(`   Value: $${message.spentDollars}`);
  console.log(`   Amount: ${message.gotToken.amount} ${message.gotToken.symbol}`);

  ws.send(JSON.stringify(message));
}

function connectAndTest() {
  console.log('🔌 Attempting to connect to WebSocket server at ws://localhost:2137...');

  let reconnectAttempts = 0;
  const maxReconnectAttempts = 50; // Try for a long time
  const reconnectDelay = 2000; // 2 seconds between attempts

  function attemptConnection() {
    console.log(`🔄 Connection attempt ${reconnectAttempts + 1}/${maxReconnectAttempts}...`);

    const ws = new WebSocket('ws://localhost:2137');

    ws.on('open', function open() {
      console.log('✅ Connected to WebSocket server');
      console.log('🧪 Starting test sequence...\n');

      // Send first test message immediately
      console.log('📤 Sending first test message...');
      sendTestMessage(ws, 0);

      // Send additional test messages with delays
      setTimeout(() => {
        console.log('📤 Sending second test message...');
        sendTestMessage(ws, 1);
      }, 2000);
      setTimeout(() => {
        console.log('📤 Sending third test message...');
        sendTestMessage(ws, 2);
      }, 4000);
      setTimeout(() => {
        console.log('📤 Sending fourth test message...');
        sendTestMessage(ws, 3);
      }, 6000);

      // Close connection after tests
      setTimeout(() => {
        console.log('\n✅ Test sequence completed');
        console.log('🔌 Closing WebSocket connection...');
        ws.close();
      }, 8000);
    });

    ws.on('message', function message(data) {
      console.log('📨 Received response:', data.toString());
    });

    ws.on('close', function close(code, reason) {
      if (code !== 1000) { // 1000 = normal closure
        console.log(`🔌 WebSocket connection closed unexpectedly (code: ${code})`);
        // Don't reconnect on close, only on error
      } else {
        console.log('🔌 WebSocket connection closed normally');
      }
    });

    ws.on('error', function error(err) {
      console.error(`❌ Connection attempt ${reconnectAttempts + 1} failed:`, err.message);

      reconnectAttempts++;

      if (reconnectAttempts < maxReconnectAttempts) {
        console.log(`⏳ Retrying in ${reconnectDelay / 1000} seconds... (${reconnectAttempts}/${maxReconnectAttempts})`);
        setTimeout(attemptConnection, reconnectDelay);
      } else {
        console.error('❌ Max reconnection attempts reached. WebSocket server may not be running.');
        console.log('💡 Make sure the Fetcher service is running on port 2137');
        console.log('💡 Or update the WebSocket URL in this test file');
        process.exit(1);
      }
    });
  }

  // Start the first connection attempt
  attemptConnection();
}

// Command line options
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log('🧪 WebSocket Test Tool for OpenChat Buy Bot');
  console.log('');
  console.log('Usage: node test-websocket.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --single       Send only one test message');
  console.log('  --continuous   Send messages continuously every 5 seconds');
  console.log('');
  console.log('Default: Send 4 test messages with 2-second intervals');
  process.exit(0);
}

if (args.includes('--single')) {
  console.log('🧪 Single message test mode');
  const ws = new WebSocket('ws://localhost:2137');
  ws.on('open', () => {
    sendTestMessage(ws, 0);
    setTimeout(() => ws.close(), 1000);
  });
  ws.on('error', (err) => console.error('❌ Error:', err.message));
} else if (args.includes('--continuous')) {
  console.log('🧪 Continuous test mode (Ctrl+C to stop)');
  let messageCount = 0;

  function sendContinuous() {
    const ws = new WebSocket('ws://localhost:2137');
    ws.on('open', () => {
      sendTestMessage(ws, messageCount % testMessages.length);
      messageCount++;
      setTimeout(() => ws.close(), 500);
    });
    ws.on('error', (err) => console.error('❌ Error:', err.message));
  }

  // Send immediately, then every 5 seconds
  sendContinuous();
  setInterval(sendContinuous, 5000);
} else {
  // Default: Send test sequence
  connectAndTest();
}
