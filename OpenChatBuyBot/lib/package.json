{"name": "@open-ic/openchat-botclient-ts", "version": "1.0.56", "description": "A client library for OpenChat bots to interact with the OpenChat backend", "main": "lib/index.cjs", "module": "lib/index.mjs", "types": "lib/index.d.ts", "scripts": {"build": "rollup -c", "test": "vitest", "test:watch": "vitest -w", "prepare": "npm run build", "publish": "npm publish --access public"}, "author": "<EMAIL>", "license": "ISC", "files": ["lib/", "src/", "package.json", "README.md"], "devDependencies": {"@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-typescript": "^12.1.2", "@types/jsonwebtoken": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "rollup": "^4.28.1", "typescript": "^5.7.2", "vitest": "^3.0.6"}, "dependencies": {"@dfinity/agent": "^2.2.0", "@dfinity/candid": "^2.2.0", "@dfinity/identity-secp256k1": "^2.2.0", "@dfinity/principal": "^2.2.0", "@sinclair/typebox": "^0.34.16", "js-sha3": "^0.9.3", "jsonwebtoken": "^9.0.2", "msgpackr": "^1.11.2"}}