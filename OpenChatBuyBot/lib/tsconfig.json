{"include": ["src/**/*"], "compilerOptions": {"allowJs": true, "checkJs": false, "declaration": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "lib": ["DOM", "DOM.Iterable", "es2020", "WebWorker"], "module": "es2020", "moduleResolution": "node", "noUnusedLocals": true, "noUnusedParameters": true, "paths": {}, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "es2020", "verbatimModuleSyntax": true, "outDir": "lib"}}