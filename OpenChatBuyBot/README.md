# OpenChat Buy Bot

A Telegram-bot-like trading alert system for OpenChat that monitors token buy activity across ICP DEXs and sends real-time notifications to OpenChat groups.

## Features

### 🔔 Real-time Buy Alerts
- **Buy Alerts**: Get notified when someone buys your watched tokens
- **Trade Size Indicators**: W<PERSON> 🐋, <PERSON> 🦈, <PERSON> 🐟, Shrimp 🦐 emojis
- **Detailed Trade Info**: Market cap, holder data, price information

### 💱 Multi-DEX Support
- ICPSwap
- KongSwap  
- bob.fun

### ⚙️ Advanced Configuration
- Customizable minimum trade amounts
- Multi-group support
- Persistent token watchlists
- Real-time WebSocket integration

## Commands

| Command | Description | Usage |
|---------|-------------|-------|
| `/register` | Add a token to watch list | `/register <token_address> [min_amount]` |
| `/unregister` | Remove token from watch list | `/unregister <token_address>` |
| `/list` | Show all watched tokens | `/list` |
| `/status` | Show bot status and stats | `/status` |
| `/help` | Show help information | `/help` |

## Examples

```
/register ryjl3-tyaaa-aaaaa-aaaba-cai
/register ryjl3-tyaaa-aaaaa-aaaba-cai 100
/unregister ryjl3-tyaaa-aaaaa-aaaba-cai
/list
/status
```

## Setup

### Prerequisites
- Node.js 18+
- Access to OpenChat bot credentials
- Running Fetcher WebSocket service

### Installation

1. **Clone and install dependencies:**
```bash
cd OpenChatBuyBot
npm install
```

2. **Configure environment variables:**
```bash
cp .env.example .env
# Edit .env with your OpenChat bot credentials
```

3. **Required environment variables:**
```env
# OpenChat Bot Configuration
OC_PUBLIC="-----BEGIN PUBLIC KEY-----..."
IDENTITY_PRIVATE="-----BEGIN EC PRIVATE KEY-----..."
IC_HOST="https://icp-api.io"
STORAGE_INDEX_CANISTER="rturd-qaaaa-aaaaf-aabaq-cai"

# WebSocket Configuration
WEBSOCKET_URL="ws://localhost:2137"
PORT=2222
```

### Running

**Development:**
```bash
npm run dev
```

**Production:**
```bash
npm run build
npm start
```

## Architecture

### Core Components

1. **WebSocket Client** (`src/services/websocket-client.ts`)
   - Connects to Fetcher service on port 2137
   - Handles real-time trading events
   - Auto-reconnection with exponential backoff

2. **Storage Service** (`src/services/storage.ts`)
   - In-memory storage for group configurations
   - Token watchlist management
   - Group activity tracking

3. **Message Service** (`src/services/message.ts`)
   - Formats trading alerts for OpenChat
   - Handles different message types
   - Emoji indicators for trade sizes and DEXs

4. **Command Handlers** (`src/handlers/`)
   - `/register` - Add tokens to watchlist
   - `/unregister` - Remove tokens
   - `/list` - Show watched tokens
   - `/status` - Bot statistics
   - `/help` - Usage instructions

### Message Flow

```
Fetcher WebSocket → WebSocket Client → Message Service → OpenChat Groups
                                   ↓
                              Storage Service (Group/Token Management)
```

### Data Structures

**Group Configuration:**
```typescript
interface GroupConfig {
  groupId: string;
  groupName?: string;
  watchedTokens: TokenWatchConfig[];
  isActive: boolean;
  addedBy: string;
  addedAt: Date;
  lastActivity?: Date;
}
```

**Token Watch Configuration:**
```typescript
interface TokenWatchConfig {
  tokenAddress: string;
  tokenSymbol: string;
  tokenName: string;
  minTradeAmount?: number;
  priceAlerts?: boolean;
  volumeAlerts?: boolean;
  addedBy: string;
  addedAt: Date;
}
```

## Integration with Fetcher Service

The bot connects to the Fetcher WebSocket service to receive real-time trading events:

- **Buy Messages**: Token purchase notifications with detailed trade information

Events are filtered based on group watchlists and forwarded to appropriate OpenChat groups.

## Development

### Project Structure
```
src/
├── handlers/           # Command handlers
├── services/          # Core services
├── middleware/        # Express middleware
├── types.ts          # TypeScript interfaces
├── schema.ts         # OpenChat bot schema
├── app.ts           # Express app setup
└── server.ts        # Server entry point
```

### Adding New Commands

1. Create handler in `src/handlers/`
2. Add to command router in `src/handlers/executeCommands.ts`
3. Update schema in `src/schema.ts`

### Testing

```bash
# Start the bot
npm run dev

# In another terminal, test WebSocket connection
# The bot will automatically connect to ws://localhost:2137
```

## Deployment

### Docker (Recommended)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 2222
CMD ["npm", "start"]
```

### Environment Setup

Ensure the Fetcher WebSocket service is running and accessible at the configured URL.

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check if Fetcher service is running
   - Verify WEBSOCKET_URL in .env
   - Check firewall/network connectivity

2. **Bot Commands Not Working**
   - Verify OpenChat credentials in .env
   - Check bot permissions in OpenChat
   - Review server logs for errors

3. **No Trading Alerts**
   - Confirm tokens are registered with `/list`
   - Check WebSocket connection with `/status`
   - Verify Fetcher service is broadcasting events

### Logs

The bot provides detailed logging:
- WebSocket connection status
- Command execution
- Message processing
- Error handling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
