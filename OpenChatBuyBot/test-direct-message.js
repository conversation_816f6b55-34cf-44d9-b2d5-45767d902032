const axios = require('axios');

// Test message data for FUNNAI token
const testBuyMessage = {
  "spentToken": {
    "address": "ryjl3-tyaaa-aaaaa-aaaba-cai",
    "symbol": "ICP",
    "name": "ICP",
    "amount": 5.0,
    "priceUSD": 4.82,
    "pricePairToken": 10.44
  },
  "gotToken": {
    "address": "vpyot-zqaaa-aaaaa-qavaq-cai",
    "symbol": "FUNNAI",
    "name": "FUNNAI",
    "amount": 52.2,
    "priceUSD": 0.46,
    "pricePairToken": 0.095
  },
  "pairAddress": "c5u7l-rqaaa-aaaar-qbqta-cai",
  "spentDollars": 24.10,
  "holderIncrease": "+15.2%",
  "holderWallet": "test-wallet-address",
  "marketcap": 135000,
  "dex": "ICP Swap",
  "type": "NewBuy",
  "timestamp": new Date().toISOString().slice(0, 16).replace('T', ' '),
  "holderPercentageOwned": 0.0012
};

async function testDirectMessage() {
  console.log('🧪 Testing direct message processing...');
  console.log('📤 Test message data:');
  console.log(`   Token: ${testBuyMessage.gotToken.symbol}`);
  console.log(`   Value: $${testBuyMessage.spentDollars}`);
  console.log(`   Amount: ${testBuyMessage.gotToken.amount} ${testBuyMessage.gotToken.symbol}`);
  console.log('');

  try {
    // Send HTTP POST request directly to the bot's internal processing
    // This simulates what would happen if the WebSocket received the message
    const response = await axios.post('http://localhost:3002/test-buy-message', testBuyMessage, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('✅ Response received:', response.status);
    if (response.data) {
      console.log('📨 Response data:', response.data);
    }

  } catch (error) {
    if (error.response) {
      console.log('❌ HTTP Error:', error.response.status, error.response.statusText);
      if (error.response.data) {
        console.log('📋 Error details:', error.response.data);
      }
    } else if (error.code === 'ECONNREFUSED') {
      console.log('❌ Connection refused - make sure the OpenChat bot is running on port 3002');
    } else {
      console.log('❌ Error:', error.message);
    }
  }
}

async function testHealthCheck() {
  console.log('🔍 Testing bot health check...');
  try {
    const response = await axios.get('http://localhost:3002/health', { timeout: 5000 });
    console.log('✅ Bot is running:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Bot is not running or not responding');
    return false;
  }
}

async function main() {
  console.log('🧪 OpenChat Buy Bot Direct Message Test');
  console.log('=====================================\n');

  // First check if bot is running
  const isRunning = await testHealthCheck();
  if (!isRunning) {
    console.log('💡 Please start the OpenChat bot first with: npm run dev');
    return;
  }

  console.log('');
  
  // Test direct message processing
  await testDirectMessage();
  
  console.log('\n🏁 Test completed');
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log('🧪 Direct Message Test Tool for OpenChat Buy Bot');
  console.log('');
  console.log('Usage: node test-direct-message.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('');
  console.log('This tool sends a test buy message directly to the bot\'s HTTP endpoint');
  console.log('instead of trying to connect to the WebSocket server.');
  process.exit(0);
}

main();
