import { BotClientFactory, ChatActionScope, Permissions, GroupChatIdentifier } from '@open-ic/openchat-botclient-ts';

// Test autonomous messaging
async function testAutonomousMessaging() {
  console.log('🧪 Testing autonomous messaging...');
  
  try {
    const factory = new BotClientFactory({
      openchatPublicKey: process.env.OC_PUBLIC!,
      icHost: process.env.IC_HOST!,
      identityPrivateKey: process.env.IDENTITY_PRIVATE!,
      openStorageCanisterId: process.env.STORAGE_INDEX_CANISTER!,
    });

    const groupId = 'c2gkr-xyaaa-aaaac-a3hga-cai';
    const chatIdentifier = new GroupChatIdentifier(groupId);
    const scope = new ChatActionScope(chatIdentifier);
    
    const permissions = Permissions.encodePermissions({
      community: [],
      chat: ["ReadMessages"],
      message: ["Text"]
    });
    
    const apiGateway = process.env.IC_HOST || 'https://ic0.app';
    
    console.log('✅ Creating autonomous client...');
    const autonomousClient = factory.createClientInAutonomouseContext(
      scope,
      apiGateway,
      new Permissions(permissions)
    );
    
    console.log('✅ Autonomous client created successfully!');
    console.log('✅ Test passed - autonomous messaging setup works');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testAutonomousMessaging();
