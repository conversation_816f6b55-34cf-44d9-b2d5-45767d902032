# Admin Features Documentation

The OpenChat Buy Bot now includes comprehensive admin functionality to ensure only authorized users can manage token watchlists.

## Admin System Overview

### Key Features
- **Group Creator**: Automatically becomes the first admin
- **Admin Management**: Existing admins can add/remove other admins
- **Token Management**: Only admins can register/unregister tokens
- **Permission Checks**: All sensitive operations require admin privileges

### Admin Hierarchy
1. **Group Creator** (👑): The user who first registers a token in the group
   - Cannot be removed as admin
   - Has all admin privileges
   - Can add/remove other admins

2. **Additional Admins** (⭐): Users granted admin privileges
   - Can register/unregister tokens
   - Can add/remove other admins (except group creator)
   - Can be removed by other admins

3. **Regular Members** (👤): All other group members
   - Can view token lists and bot status
   - Cannot manage tokens or admins
   - Must contact admins for token management

## Commands

### Admin Management Commands

#### `/admin list`
Shows all group admins with their roles.

**Example Output:**
```
👥 Group Administration

Group Admins (2):
1. 👑 `user123` (Creator)
2. ⭐ `user456` (Admin)

Note: Only admins can register/unregister tokens.
```

#### `/admin add <user_id>`
Adds a user as a group admin (admin only).

**Usage:** `/admin add abc123def456`

**Success Response:**
```
✅ Admin Added Successfully!

New Admin: `abc123def456`
Added By: `current_user_id`

The user can now register and unregister tokens.
```

#### `/admin remove <user_id>`
Removes admin privileges from a user (admin only).

**Usage:** `/admin remove abc123def456`

**Success Response:**
```
✅ Admin Removed Successfully!

Removed Admin: `abc123def456`
Removed By: `current_user_id`

The user can no longer manage tokens.
```

#### `/admin status`
Shows your admin status and permissions.

**Example Output:**
```
📊 Your Admin Status

Status: 👑 You are the group creator (admin)

Group Admins (2):
1. 👑 `user123` (Creator)
2. ⭐ `user456` (Admin)

Permissions:
• Register/unregister tokens: ✅
• Manage admins: ✅
```

### Token Management (Admin Only)

#### `/register <token_address> [min_amount]`
Registers a token for monitoring (admin only).

**Permission Check:**
- If user is not admin: Shows permission denied message
- If user is admin: Proceeds with token registration

#### `/unregister <token_address>`
Removes a token from monitoring (admin only).

**Permission Check:**
- If user is not admin: Shows permission denied message
- If user is admin: Proceeds with token removal

## Permission Denied Messages

When non-admins try to use admin commands, they receive helpful error messages:

### Token Management Attempt
```
❌ Permission Denied

Only group admins can register tokens.

Your Status: 👤 You are a group member (contact an admin to manage tokens)

Contact a group admin to register tokens or to be granted admin permissions.
```

### Admin Management Attempt
```
❌ Permission Denied

Only existing admins can add new admins.

Your Status: 👤 You are a group member (contact an admin to manage tokens)
```

## Implementation Details

### Data Structure
```typescript
interface GroupConfig {
  groupId: string;
  groupName?: string;
  watchedTokens: TokenWatchConfig[];
  isActive: boolean;
  addedBy: string; // Group creator (always admin)
  addedAt: Date;
  lastActivity?: Date;
  admins: string[]; // Additional admins
}
```

### Admin Check Logic
1. **Group Creator Check**: `groupConfig.addedBy === userId`
2. **Admin List Check**: `groupConfig.admins.includes(userId)`
3. **Combined Check**: Creator OR in admin list = admin privileges

### Security Features
- **Input Validation**: All user IDs are validated
- **Duplicate Prevention**: Cannot add existing admins
- **Creator Protection**: Group creator cannot be removed
- **Permission Inheritance**: All admin operations require existing admin privileges

## Best Practices

### For Group Creators
1. **Add Trusted Admins**: Only grant admin privileges to trusted users
2. **Regular Review**: Periodically review the admin list
3. **Clear Communication**: Inform group members about admin policies

### For Group Admins
1. **Responsible Management**: Only register relevant tokens
2. **Coordinate Changes**: Discuss major changes with other admins
3. **Monitor Activity**: Keep track of token performance and group needs

### For Group Members
1. **Request Process**: Contact admins for token additions/removals
2. **Provide Details**: Include token address and reasoning when requesting
3. **Respect Decisions**: Understand that admins manage tokens for the group's benefit

## Migration from Previous Version

Existing groups without admin configuration will be automatically upgraded:
- The first user to register a token becomes the group creator
- Empty admin list is initialized
- All existing functionality remains unchanged

## Troubleshooting

### Common Issues

1. **"Permission Denied" for Group Creator**
   - Check if group configuration exists
   - Verify user ID matches group creator

2. **Cannot Add Admin**
   - Ensure requester is an admin
   - Check if target user is already an admin

3. **Cannot Remove Admin**
   - Verify requester has admin privileges
   - Cannot remove group creator
   - Check if target user is actually an admin

### Debug Commands
- `/admin status` - Check your permissions
- `/admin list` - View all admins
- `/status` - View overall bot status

## Future Enhancements

Potential future features:
- **Role-based Permissions**: Different admin levels with specific permissions
- **Audit Log**: Track all admin actions and changes
- **Bulk Operations**: Add/remove multiple admins at once
- **OpenChat Integration**: Sync with OpenChat group admin roles
- **Time-limited Admin**: Temporary admin privileges with expiration
