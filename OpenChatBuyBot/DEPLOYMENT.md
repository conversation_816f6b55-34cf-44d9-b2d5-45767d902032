# OpenChat Buy Bot Deployment Guide

This guide covers deploying the OpenChat Buy Bot using Cloudflare tunnels for secure, public access.

## 🚀 Quick Start

### Prerequisites

1. **Node.js** (v18 or higher)
2. **npm** (comes with Node.js)
3. **Cloudflared binary** (located at `../cloudflared`)
4. **OpenChat bot credentials** (IDENTITY_PRIVATE and OC_PUBLIC keys)

### 1. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit with your credentials
nano .env
```

Required environment variables:
- `IDENTITY_PRIVATE` - Your bot's private key (from OpenChat)
- `OC_PUBLIC` - OpenChat public key (from OpenChat)
- `IC_HOST` - ICP network host (default: https://icp-api.io)
- `STORAGE_INDEX_CANISTER` - Storage canister ID
- `WEBSOCKET_URL` - Fetcher service WebSocket URL

### 2. Development Deployment

For testing and development:

```bash
# Make scripts executable
chmod +x *.sh

# Start development deployment
./deploy.sh
```

This will:
- ✅ Install dependencies
- ✅ Build the project
- ✅ Start the bot on localhost:3002
- ✅ Create Cloudflare tunnel
- ✅ Provide public URL for OpenChat registration

### 3. Production Deployment

For persistent, production-ready deployment:

```bash
# Install PM2 globally (if not installed)
npm install -g pm2

# Start production deployment
./deploy-production.sh
```

This will:
- ✅ Use PM2 for process management
- ✅ Auto-restart on crashes
- ✅ Log management
- ✅ Persistent deployment across reboots

## 📊 Management Commands

### Check Deployment Status
```bash
./check-deployment.sh
```

### View Logs (Production)
```bash
# Bot logs
pm2 logs openchat-buy-bot

# Tunnel logs
pm2 logs cloudflare-tunnel

# All logs
pm2 logs
```

### Stop Deployment
```bash
# Development
./stop-deployment.sh

# Production
./stop-production.sh
```

### Restart Services (Production)
```bash
# Restart bot only
pm2 restart openchat-buy-bot

# Restart tunnel only
pm2 restart cloudflare-tunnel

# Restart all
pm2 restart all
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `IDENTITY_PRIVATE` | Bot private key | ✅ | - |
| `OC_PUBLIC` | OpenChat public key | ✅ | - |
| `IC_HOST` | ICP network endpoint | ✅ | https://icp-api.io |
| `STORAGE_INDEX_CANISTER` | Storage canister ID | ✅ | - |
| `WEBSOCKET_URL` | Fetcher WebSocket URL | ❌ | ws://localhost:2137 |
| `PORT` | Bot server port | ❌ | 3002 |

### Cloudflare Tunnel

The deployment uses Cloudflare's free tunnel service to expose your local bot to the internet. The tunnel:

- ✅ Provides HTTPS automatically
- ✅ No port forwarding needed
- ✅ Secure connection to OpenChat
- ✅ Dynamic URL generation

## 🌐 OpenChat Integration

### 1. Get Your Tunnel URL

After deployment, you'll see:
```
✅ Cloudflare tunnel URL: https://your-unique-id.trycloudflare.com
```

### 2. Register Bot in OpenChat

1. Go to OpenChat
2. Navigate to bot registration
3. Use your tunnel URL as the bot endpoint
4. Test with `/register` command in a group

### 3. Available Endpoints

- `GET /health` - Health check
- `GET /bot_definition` - Bot schema for OpenChat
- `POST /execute_command` - Command execution

## 🔍 Troubleshooting

### Bot Not Starting

```bash
# Check logs
./check-deployment.sh

# View detailed logs (production)
pm2 logs openchat-buy-bot --lines 50

# Restart bot
pm2 restart openchat-buy-bot
```

### Tunnel Issues

```bash
# Check tunnel status
./check-deployment.sh

# View tunnel logs
pm2 logs cloudflare-tunnel

# Restart tunnel
pm2 restart cloudflare-tunnel
```

### Environment Issues

```bash
# Verify environment
./check-deployment.sh

# Check .env file
cat .env

# Reload environment (production)
pm2 restart all --update-env
```

### Common Issues

1. **Port 3002 already in use**
   ```bash
   lsof -ti:3002 | xargs kill -9
   ```

2. **Cloudflared not found**
   - Ensure `../cloudflared` exists and is executable
   - Download from: https://github.com/cloudflare/cloudflared/releases

3. **Permission denied**
   ```bash
   chmod +x *.sh
   ```

4. **Build failures**
   ```bash
   rm -rf node_modules dist
   npm install
   npm run build
   ```

## 📈 Monitoring

### PM2 Monitoring (Production)

```bash
# Real-time monitoring
pm2 monit

# Process status
pm2 status

# Resource usage
pm2 show openchat-buy-bot
```

### Health Checks

```bash
# Local health check
curl http://localhost:3002/health

# Tunnel health check
curl https://your-tunnel-url.trycloudflare.com/health
```

## 🔄 Updates and Maintenance

### Updating the Bot

```bash
# Pull latest changes
git pull

# Rebuild and restart (production)
npm run build
pm2 restart openchat-buy-bot

# Or use full redeployment
./deploy-production.sh
```

### Log Rotation

Logs are automatically managed by PM2 in production mode. Manual cleanup:

```bash
# Clear logs
pm2 flush

# Rotate logs
pm2 reloadLogs
```

## 🛡️ Security

- ✅ Environment variables stored in `.env` (not committed)
- ✅ HTTPS via Cloudflare tunnel
- ✅ No exposed ports on your machine
- ✅ Secure WebSocket connections

## 📞 Support

If you encounter issues:

1. Run `./check-deployment.sh` for diagnostics
2. Check logs with `pm2 logs` (production) or console output (development)
3. Verify environment variables are correctly set
4. Ensure Fetcher service is running and accessible
