#!/bin/bash

# OpenChat Buy Bot Deployment Script
echo "🚀 OpenChat Buy Bot Deployment"
echo "================================"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found!"
    echo "📋 Please copy .env.example to .env and fill in your credentials"
    echo "   cp .env.example .env"
    echo "   nano .env  # Edit with your values"
    exit 1
fi

# Load environment variables
source .env

# Check required variables
echo "🔍 Checking environment variables..."

if [ -z "$IDENTITY_PRIVATE" ]; then
    echo "❌ IDENTITY_PRIVATE not set"
    exit 1
fi

if [ -z "$OC_PUBLIC" ]; then
    echo "❌ OC_PUBLIC not set"
    exit 1
fi

if [ -z "$IC_HOST" ]; then
    echo "❌ IC_HOST not set"
    exit 1
fi

if [ -z "$STORAGE_INDEX_CANISTER" ]; then
    echo "❌ STORAGE_INDEX_CANISTER not set"
    exit 1
fi

echo "✅ All required environment variables are set"

# Build the project
echo "🔨 Building project..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"

# Test the application locally (optional)
echo "🧪 Testing application..."
timeout 5s npm start &
sleep 2

# Test health endpoint
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ Health check passed"
else
    echo "⚠️  Health check failed (this might be normal if port is busy)"
fi

# Kill the test server
pkill -f "node dist/server.js" 2>/dev/null

echo ""
echo "🎉 Deployment preparation complete!"
echo ""
echo "📋 Next steps:"
echo "1. Push your code to GitHub"
echo "2. Go to https://railway.app"
echo "3. Connect your GitHub repository"
echo "4. Set environment variables in Railway dashboard"
echo "5. Deploy automatically"
echo ""
echo "🔗 Your bot will be available at: https://your-app.railway.app"
echo "📊 Test endpoints:"
echo "   - GET /health (health check)"
echo "   - GET /schema (bot schema)"
echo "   - POST /execute_command (OpenChat commands)"
