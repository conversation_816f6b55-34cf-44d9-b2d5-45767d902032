#!/bin/bash

# OpenChat Buy Bot Cloudflare Deployment Script
echo "🚀 OpenChat Buy Bot Cloudflare Deployment"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if cloudflared exists
CLOUDFLARED_PATH="../cloudflared"
if [ ! -f "$CLOUDFLARED_PATH" ]; then
    print_error "cloudflared not found at $CLOUDFLARED_PATH"
    print_info "Please ensure cloudflared binary is available at the specified path"
    exit 1
fi

print_status "Found cloudflared binary"

# Check if .env file exists
if [ ! -f .env ]; then
    print_error ".env file not found!"
    echo "📋 Please copy .env.example to .env and fill in your credentials"
    echo "   cp .env.example .env"
    echo "   nano .env  # Edit with your values"
    exit 1
fi

# Load environment variables
source .env

# Check required variables
echo "🔍 Checking environment variables..."

if [ -z "$IDENTITY_PRIVATE" ]; then
    print_error "IDENTITY_PRIVATE not set"
    exit 1
fi

if [ -z "$OC_PUBLIC" ]; then
    print_error "OC_PUBLIC not set"
    exit 1
fi

if [ -z "$IC_HOST" ]; then
    print_error "IC_HOST not set"
    exit 1
fi

if [ -z "$STORAGE_INDEX_CANISTER" ]; then
    print_error "STORAGE_INDEX_CANISTER not set"
    exit 1
fi

print_status "All required environment variables are set"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install dependencies"
        exit 1
    fi
    print_status "Dependencies installed"
fi

# Build the project
echo "🔨 Building project..."
npm run build

if [ $? -ne 0 ]; then
    print_error "Build failed"
    exit 1
fi

print_status "Build successful"

# Kill any existing processes on port 3002
echo "🧹 Cleaning up existing processes..."
lsof -ti:3002 | xargs kill -9 2>/dev/null || true
sleep 2

# Start the bot in background
echo "🚀 Starting OpenChat Buy Bot..."
npm run start &
BOT_PID=$!

# Wait for bot to start
sleep 5

# Test health endpoint
echo "🧪 Testing bot health..."
if curl -f http://localhost:3002/health > /dev/null 2>&1; then
    print_status "Bot health check passed"
else
    print_error "Bot health check failed"
    kill $BOT_PID 2>/dev/null
    exit 1
fi

# Start Cloudflare tunnel
echo "🌐 Starting Cloudflare tunnel..."
$CLOUDFLARED_PATH tunnel --url http://localhost:3002 &
TUNNEL_PID=$!

# Wait for tunnel to establish
sleep 10

# Get tunnel URL from cloudflared logs
TUNNEL_URL=""
for i in {1..30}; do
    # Try to extract URL from cloudflared process
    TUNNEL_URL=$(ps aux | grep cloudflared | grep -o 'https://[^[:space:]]*\.trycloudflare\.com' | head -1)
    if [ ! -z "$TUNNEL_URL" ]; then
        break
    fi
    sleep 1
done

if [ -z "$TUNNEL_URL" ]; then
    print_warning "Could not automatically detect tunnel URL"
    print_info "Check the cloudflared output above for the tunnel URL"
    TUNNEL_URL="https://your-tunnel-url.trycloudflare.com"
fi

# Test tunnel endpoint
echo "🧪 Testing tunnel connectivity..."
if curl -f "$TUNNEL_URL/health" > /dev/null 2>&1; then
    print_status "Tunnel connectivity test passed"
else
    print_warning "Tunnel connectivity test failed (tunnel might still be starting)"
fi

echo ""
echo "🎉 OpenChat Buy Bot Deployment Complete!"
echo "========================================"
echo ""
print_status "Bot is running locally on: http://localhost:3002"
print_status "Cloudflare tunnel URL: $TUNNEL_URL"
echo ""
echo "� Available endpoints:"
echo "   - GET  $TUNNEL_URL/health (health check)"
echo "   - GET  $TUNNEL_URL/bot_definition (bot schema)"
echo "   - POST $TUNNEL_URL/execute_command (OpenChat commands)"
echo ""
echo "🔧 Process IDs:"
echo "   - Bot PID: $BOT_PID"
echo "   - Tunnel PID: $TUNNEL_PID"
echo ""
echo "📋 Next steps:"
echo "1. Copy the tunnel URL above"
echo "2. Go to OpenChat and register your bot"
echo "3. Use the tunnel URL as your bot's endpoint"
echo "4. Test with /register command in your OpenChat group"
echo ""
echo "🛑 To stop the deployment:"
echo "   kill $BOT_PID $TUNNEL_PID"
echo "   or run: ./stop-deployment.sh"
echo ""

# Create stop script
cat > stop-deployment.sh << 'EOF'
#!/bin/bash
echo "� Stopping OpenChat Buy Bot deployment..."

# Kill bot process
BOT_PID=$(lsof -ti:3002)
if [ ! -z "$BOT_PID" ]; then
    kill $BOT_PID
    echo "✅ Stopped bot process (PID: $BOT_PID)"
fi

# Kill cloudflared processes
TUNNEL_PIDS=$(pgrep -f cloudflared)
if [ ! -z "$TUNNEL_PIDS" ]; then
    echo $TUNNEL_PIDS | xargs kill
    echo "✅ Stopped tunnel processes"
fi

echo "🎉 Deployment stopped successfully"
EOF

chmod +x stop-deployment.sh

# Keep the script running to monitor processes
echo "🔄 Monitoring deployment (Press Ctrl+C to stop)..."
echo "📊 Bot logs will appear below:"
echo "================================"

# Monitor both processes
while kill -0 $BOT_PID 2>/dev/null && kill -0 $TUNNEL_PID 2>/dev/null; do
    sleep 5
done

print_warning "One or more processes have stopped"
echo "🔍 Checking process status..."

if ! kill -0 $BOT_PID 2>/dev/null; then
    print_error "Bot process has stopped"
fi

if ! kill -0 $TUNNEL_PID 2>/dev/null; then
    print_error "Tunnel process has stopped"
fi

echo "🛑 Deployment monitoring ended"
