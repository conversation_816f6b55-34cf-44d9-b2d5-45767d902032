import { BotClient } from "@open-ic/openchat-botclient-ts";
import { Request } from "express";

export interface WithBotClient extends Request {
  botClient: BotClient;
}

// WebSocket message types from Fetcher service
export interface BuyTokenData {
  address: string;
  name: string;
  symbol: string;
  amount?: number;
  priceUSD?: number;
  pricePairToken?: number;
}

export interface BuyMessageData {
  spentToken: BuyTokenData;
  gotToken: BuyTokenData;
  pairAddress: string;
  spentDollars: number;
  holderIncrease?: string;
  holderWallet?: string;
  marketcap: number;
  dex: string;
  type: string;
  icpCollected?: number;
  timestamp?: string;
  holderPercentageOwned?: number;
}



// Bot-specific types
export interface TokenWatchConfig {
  tokenAddress: string;
  tokenSymbol?: string;
  tokenName?: string;
  minAmount: number; // Minimum trade amount in USD to trigger notification
  addedBy: string; // User who added this token
  addedAt: Date;
}

export interface GroupConfig {
  groupId: string;
  groupName?: string;
  watchedTokens: TokenWatchConfig[];
  isActive: boolean;
  addedBy: string;
  createdAt: Date;
  lastActivity?: Date;
  admins?: string[]; // List of user IDs who can manage tokens
}

export interface WebSocketMessage {
  type: 'buy';
  data: BuyMessageData;
}

export interface BotCommand {
  name: string;
  description: string;
  usage: string;
  handler: (req: WithBotClient, args: string[]) => Promise<void>;
}