/**
 * This is our server's entry point and just runs our express app on the specified port
 */
import "dotenv/config";
import app from "./app";
import { WebSocketClientService } from "./services/websocket-client";

const PORT = parseInt(process.env.PORT || '3000', 10);
const WEBSOCKET_URL = process.env.WEBSOCKET_URL || 'ws://localhost:2137';

// Initialize WebSocket client
const wsClient = WebSocketClientService.getInstance();

// Start the server on all interfaces (0.0.0.0) to work with Cloudflare tunnel
app.listen(PORT, '0.0.0.0', async () => {
  console.log(`🚀 OpenChat Buy Bot server is running on port ${PORT} (all interfaces)`);
  console.log(`📡 Connecting to WebSocket service at ${WEBSOCKET_URL}...`);

  try {
    await wsClient.connect(WEBSOCKET_URL);
    console.log(`✅ <PERSON><PERSON> is ready and listening for trading events!`);
  } catch (error) {
    console.error(`❌ Failed to connect to WebSocket service:`, error);
    console.log(`🔄 <PERSON><PERSON> will continue trying to reconnect automatically...`);
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down gracefully...');
  wsClient.disconnect();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down gracefully...');
  wsClient.disconnect();
  process.exit(0);
});
