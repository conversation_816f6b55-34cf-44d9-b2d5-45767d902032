import { BotDefinition, Permissions } from "@open-ic/openchat-botclient-ts";
import { Request, Response } from "express";

const emptyPermissions = {
  chat: [],
  community: [],
  message: [],
};

function getBotDefinition(): BotDefinition {
  return {
    description: "OpenChat Buy Bot - Real-time trading alerts for ICP tokens",
    // Add autonomous messaging configuration
    autonomous_config: {
      permissions: Permissions.encodePermissions({
        chat: ["ReadMessages"],
        community: [],
        message: ["Text"],
      }),
    },
    commands: [
      {
        name: "register",
        default_role: "Participant",
        description: "Add a token to the watchlist with optional minimum alert amount",
        placeholder: "Registering token...",
        permissions: Permissions.encodePermissions({
          ...emptyPermissions,
          message: ["Text"],
        }),
        params: [
          {
            name: "token_address",
            description: "The canister ID of the token to watch",
            placeholder: "e.g., ryjl3-tyaaa-aaaaa-aaaba-cai",
            param_type: {
              StringParam: {
                min_length: 1,
                max_length: 100,
                choices: [],
                multi_line: false,
              },
            },
            required: true,
          },
          {
            name: "min_amount",
            description: "Minimum USD amount to trigger alerts (optional)",
            placeholder: "e.g., 100",
            param_type: {
              DecimalParam: {
                min_value: 0,
                max_value: 1000000,
                choices: [],
              },
            },
            required: false,
          },
        ],
      },
      {
        name: "unregister",
        default_role: "Participant",
        description: "Remove a token from the watchlist",
        placeholder: "Unregistering token...",
        permissions: Permissions.encodePermissions({
          ...emptyPermissions,
          message: ["Text"],
        }),
        params: [
          {
            name: "token_address",
            description: "The canister ID of the token to remove",
            placeholder: "e.g., ryjl3-tyaaa-aaaaa-aaaba-cai",
            param_type: {
              StringParam: {
                min_length: 1,
                max_length: 100,
                choices: [],
                multi_line: false,
              },
            },
            required: true,
          },
        ],
      },
      {
        name: "list",
        default_role: "Participant",
        description: "Show all tokens being watched in this group",
        placeholder: "Loading token list...",
        permissions: Permissions.encodePermissions({
          ...emptyPermissions,
          message: ["Text"],
        }),
        params: [],
      },
      {
        name: "status",
        default_role: "Participant",
        description: "Show bot status and group statistics",
        placeholder: "Loading status...",
        permissions: Permissions.encodePermissions({
          ...emptyPermissions,
          message: ["Text"],
        }),
        params: [],
      },
      {
        name: "admin",
        default_role: "Participant",
        description: "Admin management (list, add, remove admins)",
        placeholder: "Processing admin command...",
        permissions: Permissions.encodePermissions({
          ...emptyPermissions,
          message: ["Text"],
        }),
        params: [],
      },
      {
        name: "help",
        default_role: "Participant",
        description: "Show help and usage instructions",
        placeholder: "Loading help...",
        permissions: Permissions.encodePermissions({
          ...emptyPermissions,
          message: ["Text"],
        }),
        params: [],
      },
    ],
  };
}

export default function schema(_: Request, res: Response) {
  res.status(200).json(getBotDefinition());
}