import { BotClientFactory } from "@open-ic/openchat-botclient-ts";
import cors from "cors";
import express from "express";
import executeCommand from "./handlers/executeCommands";
import schema from "./schema";
import { createCommandChatClient } from "./middleware/botclient";
import register from "./handlers/register";
import unregister from "./handlers/unregister";
import list from "./handlers/list";
import status from "./handlers/status";
import admin from "./handlers/admin";
import help from "./handlers/help";
import { MessageService } from "./services/message";

const app = express();

const factory = new BotClientFactory({
  openchatPublicKey: process.env.OC_PUBLIC!,
  icHost: process.env.IC_HOST!,
  identityPrivateKey: process.env.IDENTITY_PRIVATE!,
  openStorageCanisterId: process.env.STORAGE_INDEX_CANISTER!,
});

// Initialize MessageService with the bot client factory
const messageService = MessageService.getInstance();
messageService.setBotClientFactory(factory);

app.use(cors());

// Add request logging middleware
app.use((req, _res, next) => {
  console.log(`📥 ${req.method} ${req.path} - ${new Date().toISOString()}`);
  if (req.body) {
    console.log("📋 Body:", typeof req.body === 'string' ? req.body.substring(0, 200) : req.body);
  }
  console.log("📋 Headers:", JSON.stringify(req.headers, null, 2));
  next();
});

// Health check endpoint (no auth required)
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'OpenChat Buy Bot',
    version: '1.0.0'
  });
});



// Main execute_command endpoint (OpenChat standard)
app.post(
  "/execute_command",
  (_req, _res, next) => {
    console.log("🔍 Route matched: /execute_command");
    next();
  },
  express.text(),
  (_req, _res, next) => {
    console.log("🔍 express.text() middleware completed");
    next();
  },
  createCommandChatClient(factory),
  (_req, _res, next) => {
    console.log("🔍 createCommandChatClient middleware completed");
    next();
  },
  executeCommand
);

// Individual command endpoints (in case OpenChat calls these directly)
app.post("/register", express.text(), createCommandChatClient(factory), register as any);
app.post("/unregister", express.text(), createCommandChatClient(factory), unregister as any);
app.post("/list", express.text(), createCommandChatClient(factory), list as any);
app.post("/status", express.text(), createCommandChatClient(factory), status as any);
app.post("/admin", express.text(), createCommandChatClient(factory), admin as any);
app.post("/help", express.text(), createCommandChatClient(factory), help as any);

// Schema endpoints
app.get("/bot_definition", schema);
app.get("/", schema);

export default app;