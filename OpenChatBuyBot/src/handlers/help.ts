import { Response } from "express";
import { WithBotClient } from "../types";
import { success } from "./success";

export default async function help(req: WithBotClient, res: Response) {
  try {
    const client = req.botClient;

    const helpMessage =
      `🤖 **OpenChat Buy Bot - Help**\n\n` +

      `**📝 Commands:**\n` +
      `• \`/register <token_address> [min_amount]\` - Add token to watchlist\n` +
      `• \`/unregister <token_address>\` - Remove token from watchlist\n` +
      `• \`/list\` - Show all watched tokens\n` +
      `• \`/status\` - Show bot and group status\n` +
      `• \`/admin\` - Admin management (list/add/remove)\n` +
      `• \`/help\` - Show this help message\n\n` +

      `**🧪 TEST BUY ALERT (Sample):**\n` +
      `🦐 **BUY ALERT** 💱\n\n` +
      `**Token:** FUNNAI (FUNNAI)\n` +
      `**Trade:** 5.00 ICP → 49.25 FUNNAI\n` +
      `**Value:** $24.10\n` +
      `**Market Cap:** $140.00K\n` +
      `**DEX:** ICP Swap\n` +
      `**Time:** ${new Date().toISOString().slice(0, 16).replace('T', ' ')}\n\n` +
      `**Token Address:** \`vpyot-zqaaa-aaaaa-qavaq-cai\`\n\n` +
      `*This is what real buy alerts will look like!*\n\n` +

      `**🔔 Alert Types:**\n` +
      `• **Buy Alerts** - When someone buys your watched tokens\n\n` +

      `**💱 Supported DEXs:**\n` +
      `• ICPSwap\n` +
      `• KongSwap\n` +
      `• bob.fun\n\n` +

      `**📝 Examples:**\n` +
      `\`/register ryjl3-tyaaa-aaaaa-aaaba-cai\` - Watch ICP token (admin only)\n` +
      `\`/register ryjl3-tyaaa-aaaaa-aaaba-cai 100\` - Watch ICP with $100 minimum alert\n` +
      `\`/unregister ryjl3-tyaaa-aaaaa-aaaba-cai\` - Stop watching ICP (admin only)\n` +
      `\`/list\` - Show all watched tokens\n` +
      `\`/admin list\` - Show group admins\n` +
      `\`/admin add abc123\` - Add user as admin (admin only)\n\n` +

      `**⚙️ Features:**\n` +
      `• Real-time buy notifications via WebSocket\n` +
      `• Admin-only token management for security\n` +
      `• Configurable minimum alert amounts\n` +
      `• Multi-DEX support (ICPSwap, KongSwap, bob.fun)\n` +
      `• Group-based token watchlists\n\n` +

      `**🔐 Permissions:**\n` +
      `• **Anyone:** View lists, status, help\n` +
      `• **Admins Only:** Register/unregister tokens, manage admins\n` +
      `• **First User:** Automatically becomes admin\n\n` +

      `**💡 Tips:**\n` +
      `• Use canister IDs for token addresses\n` +
      `• Set minimum amounts to filter small trades\n` +
      `• Add multiple admins for better management\n` +
      `• Check \`/status\` for bot health and statistics\n\n` +

      `**🆘 Support:**\n` +
      `If you encounter issues, check the bot status with \`/status\` or contact the bot administrator.`;

    const msg = await client.createTextMessage(helpMessage);
    res.status(200).json(success(msg));
    client.sendMessage(msg).catch((err: unknown) =>
      console.error("sendMessage failed with: ", err)
    );

  } catch (error) {
    console.error('❌ Error in help handler:', error);
    const msg = await req.botClient.createTextMessage(
      "❌ An error occurred while displaying help. Please try again later."
    );
    res.status(200).json(success(msg));
    req.botClient.sendMessage(msg).catch((err: unknown) =>
      console.error("sendMessage failed with: ", err)
    );
  }
}
