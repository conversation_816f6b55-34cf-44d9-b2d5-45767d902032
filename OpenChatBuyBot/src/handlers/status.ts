import { Response } from "express";
import { WithBotClient } from "../types";
import { StorageService } from "../services/storage";
import { AdminService } from "../services/admin";
import { success } from "./success";

const storageService = StorageService.getInstance();
const adminService = AdminService.getInstance();

export default async function status(req: WithBotClient, res: Response) {
  try {
    const client = req.botClient;
    const groupId = client.chatId?.toString();
    const userId = client.initiator;

    if (!groupId || !userId) {
      const msg = await client.createTextMessage("❌ Unable to identify group or user.");
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Get group config
    const groupConfig = await storageService.getGroupConfig(groupId);

    // Get all group configs for global stats
    const allConfigs = await storageService.getAllGroupConfigs();
    const totalGroups = allConfigs.length;
    const totalTokens = allConfigs.reduce((sum, config) => sum + config.watchedTokens.length, 0);
    const activeGroups = allConfigs.filter(config => config.isActive).length;

    let message = `📊 **OpenChat Buy Bot Status**\n\n`;

    // Global Stats
    message += `**🌐 Global Statistics:**\n`;
    message += `• **Total Groups:** ${totalGroups}\n`;
    message += `• **Active Groups:** ${activeGroups}\n`;
    message += `• **Total Watched Tokens:** ${totalTokens}\n`;
    message += `• **Bot Uptime:** ${process.uptime().toFixed(0)}s\n\n`;

    // Current Group Stats
    if (groupConfig) {
      message += `**📋 Current Group:**\n`;
      message += `• **Group ID:** ${groupId}\n`;
      message += `• **Watched Tokens:** ${groupConfig.watchedTokens.length}\n`;
      message += `• **Admins:** ${groupConfig.admins?.length || 0}\n`;
      message += `• **Status:** ${groupConfig.isActive ? '🟢 Active' : '🔴 Inactive'}\n`;
      message += `• **Created:** ${groupConfig.createdAt.toLocaleDateString()}\n`;
      message += `• **Last Activity:** ${groupConfig.lastActivity?.toLocaleDateString() || 'Unknown'}\n\n`;

      // User Status
      const isAdmin = await adminService.isGroupAdmin(groupId, userId);
      const canManage = await adminService.canManageTokens(groupId, userId);

      message += `**👤 Your Status:**\n`;
      message += `• **User ID:** ${userId}\n`;
      message += `• **Admin:** ${isAdmin ? '✅ Yes' : '❌ No'}\n`;
      message += `• **Can Manage Tokens:** ${canManage ? '✅ Yes' : '❌ No'}\n\n`;

      // Recent Tokens
      if (groupConfig.watchedTokens.length > 0) {
        message += `**🔍 Recently Added Tokens:**\n`;
        const recentTokens = groupConfig.watchedTokens
          .sort((a, b) => b.addedAt.getTime() - a.addedAt.getTime())
          .slice(0, 3);

        recentTokens.forEach((token, index) => {
          message += `${index + 1}. \`${token.tokenAddress}\` ($${token.minAmount})\n`;
        });
        message += `\n`;
      }
    } else {
      message += `**📋 Current Group:**\n`;
      message += `• **Status:** Not configured\n`;
      message += `• **Action:** Use \`/register\` to start watching tokens\n\n`;
    }

    // System Info
    message += `**⚙️ System Information:**\n`;
    message += `• **Node.js:** ${process.version}\n`;
    message += `• **Memory Usage:** ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB\n`;
    message += `• **Platform:** ${process.platform}\n\n`;

    // Available Commands
    message += `**🤖 Available Commands:**\n`;
    message += `• \`/register <token>\` - Add token to watchlist\n`;
    message += `• \`/unregister <token>\` - Remove token\n`;
    message += `• \`/list\` - Show watched tokens\n`;
    message += `• \`/status\` - Show this status\n`;
    message += `• \`/admin\` - Admin management\n`;
    message += `• \`/help\` - Show help information`;

    const msg = await client.createTextMessage(message);
    res.status(200).json(success(msg));
    client.sendMessage(msg).catch((err: unknown) =>
      console.error("sendMessage failed with: ", err)
    );

  } catch (error) {
    console.error('❌ Error in status handler:', error);
    const msg = await req.botClient.createTextMessage(
      "❌ An error occurred while retrieving status information. Please try again later."
    );
    res.status(200).json(success(msg));
    req.botClient.sendMessage(msg).catch((err: unknown) =>
      console.error("sendMessage failed with: ", err)
    );
  }
}
