import { Response } from "express";
import { WithBotClient } from "../types";
import { StorageService } from "../services/storage";
import { AdminService } from "../services/admin";
import { success } from "./success";

const storageService = StorageService.getInstance();
const adminService = AdminService.getInstance();

export default async function admin(req: WithBotClient, res: Response) {
  try {
    const client = req.botClient;
    const groupId = client.chatId?.toString();
    const userId = client.initiator;

    if (!groupId || !userId) {
      const msg = await client.createTextMessage("❌ Unable to identify group or user.");
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Get group config
    let groupConfig = await storageService.getGroupConfig(groupId);
    if (!groupConfig) {
      groupConfig = {
        groupId,
        watchedTokens: [],
        admins: [userId], // First user becomes admin
        isActive: true,
        addedBy: userId,
        createdAt: new Date(),
        lastActivity: new Date()
      };
      await storageService.saveGroupConfig(groupConfig);
    }

    // Check if user is admin
    const isAdmin = await adminService.isGroupAdmin(groupId, userId);

    // Parse command arguments using named parameters
    const action = client.stringArg('action')?.toLowerCase().trim() || 'list';
    const targetUserId = client.stringArg('user_id')?.trim() || '';

    if (!action || action === 'list') {
      // Show admin status and list
      let message = `👑 **Admin Management**\n\n`;

      message += `**Your Status:** ${isAdmin ? '✅ Admin' : '❌ Not Admin'}\n\n`;

      if (groupConfig.admins && groupConfig.admins.length > 0) {
        message += `**Current Admins (${groupConfig.admins.length}):**\n`;
        groupConfig.admins.forEach((adminId, index) => {
          message += `${index + 1}. ${adminId}\n`;
        });
        message += `\n`;
      } else {
        message += `**Current Admins:** None\n\n`;
      }

      if (isAdmin) {
        message += `**Admin Commands:**\n`;
        message += `• \`/admin add <user_id>\` - Add user as admin\n`;
        message += `• \`/admin remove <user_id>\` - Remove admin\n`;
        message += `• \`/admin list\` - Show all admins\n\n`;
        message += `**Permissions:**\n`;
        message += `• ✅ Register/unregister tokens\n`;
        message += `• ✅ Manage other admins\n`;
        message += `• ✅ View all group settings`;
      } else {
        message += `**To become an admin:**\n`;
        message += `Ask an existing admin to run:\n`;
        message += `\`/admin add ${client.initiator}\``;
      }

      const msg = await client.createTextMessage(message);
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    if (action === 'list') {
      // List all admins
      let message = `👑 **Admin List**\n\n`;

      if (groupConfig.admins && groupConfig.admins.length > 0) {
        message += `**Admins (${groupConfig.admins.length}):**\n`;
        groupConfig.admins.forEach((adminId, index) => {
          message += `${index + 1}. ${adminId}\n`;
        });
      } else {
        message += `**No admins found.**\n\n`;
        message += `The first user to register a token will become an admin.`;
      }

      const msg = await client.createTextMessage(message);
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // For add/remove actions, user must be admin
    if (!isAdmin) {
      const msg = await client.createTextMessage(
        `❌ **Permission Denied**\n\n` +
        `Only admins can manage other admins.\n\n` +
        `**Your Status:** Not Admin\n\n` +
        `Contact an existing admin to be granted admin permissions.`
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    if (action === 'add') {
      if (!targetUserId) {
        const msg = await client.createTextMessage(
          "❌ Please provide a user ID to add as admin.\n\n" +
          "**Usage:** `/admin add <user_id>`"
        );
        res.status(200).json(success(msg));
        client.sendMessage(msg).catch((err: unknown) =>
          console.error("sendMessage failed with: ", err)
        );
        return;
      }
      const success_result = await adminService.addGroupAdmin(groupId, client.initiator!, targetUserId);

      if (success_result) {
        const msg = await client.createTextMessage(
          `✅ **Admin Added Successfully!**\n\n` +
          `**User:** ${userId}\n` +
          `**Added By:** ${client.initiator}\n\n` +
          `${userId} can now register/unregister tokens and manage admins.`
        );
        res.status(200).json(success(msg));
        client.sendMessage(msg).catch((err: unknown) =>
          console.error("sendMessage failed with: ", err)
        );
      } else {
        const msg = await client.createTextMessage(
          `❌ User ${userId} is already an admin or an error occurred.`
        );
        res.status(200).json(success(msg));
        client.sendMessage(msg).catch((err: unknown) =>
          console.error("sendMessage failed with: ", err)
        );
      }

    } else if (action === 'remove') {
      if (!targetUserId) {
        const msg = await client.createTextMessage(
          "❌ Please provide a user ID to remove from admins.\n\n" +
          "**Usage:** `/admin remove <user_id>`"
        );
        res.status(200).json(success(msg));
        client.sendMessage(msg).catch((err: unknown) =>
          console.error("sendMessage failed with: ", err)
        );
        return;
      }

      // Prevent removing self if they're the only admin
      if (targetUserId === client.initiator && groupConfig.admins && groupConfig.admins.length === 1) {
        const msg = await client.createTextMessage(
          `❌ Cannot remove yourself as the only admin.\n\n` +
          `Add another admin first before removing yourself.`
        );
        res.status(200).json(success(msg));
        client.sendMessage(msg).catch((err: unknown) =>
          console.error("sendMessage failed with: ", err)
        );
        return;
      }

      const success_result = await adminService.removeGroupAdmin(groupId, client.initiator!, targetUserId);

      if (success_result) {
        const msg = await client.createTextMessage(
          `✅ **Admin Removed Successfully!**\n\n` +
          `**User:** ${userId}\n` +
          `**Removed By:** ${client.initiator}\n\n` +
          `${userId} can no longer manage tokens or admins.`
        );
        res.status(200).json(success(msg));
        client.sendMessage(msg).catch((err: unknown) =>
          console.error("sendMessage failed with: ", err)
        );
      } else {
        const msg = await client.createTextMessage(
          `❌ User ${userId} is not an admin or an error occurred.`
        );
        res.status(200).json(success(msg));
        client.sendMessage(msg).catch((err: unknown) =>
          console.error("sendMessage failed with: ", err)
        );
      }

    } else {
      const msg = await client.createTextMessage(
        `❌ Unknown admin action: ${action}\n\n` +
        `**Available actions:**\n` +
        `• \`/admin\` - Show admin status\n` +
        `• \`/admin list\` - List all admins\n` +
        `• \`/admin add <user_id>\` - Add admin\n` +
        `• \`/admin remove <user_id>\` - Remove admin`
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
    }

  } catch (error) {
    console.error('❌ Error in admin handler:', error);
    const msg = await req.botClient.createTextMessage(
      "❌ An error occurred while processing admin command. Please try again later."
    );
    res.status(200).json(success(msg));
    req.botClient.sendMessage(msg).catch((err: unknown) =>
      console.error("sendMessage failed with: ", err)
    );
  }
}
