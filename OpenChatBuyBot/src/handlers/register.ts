import { Response } from "express";
import { argumentsInvalid } from "@open-ic/openchat-botclient-ts";
import { WithBotClient } from "../types";
import { StorageService } from "../services/storage";
import { AdminService } from "../services/admin";
import { MessageService } from "../services/message";
import { success } from "./success";

const storageService = StorageService.getInstance();
const adminService = AdminService.getInstance();
const messageService = MessageService.getInstance();

export default async function register(req: WithBotClient, res: Response) {
  const client = req.botClient;

  // Extract arguments using the correct OpenChat pattern (like your example)
  const tokenAddress = client.stringArg("token_address");
  const minAmount = client.decimalArg("min_amount");

  console.log("🔍 Register arguments:");
  console.log("  - tokenAddress:", tokenAddress);
  console.log("  - minAmount:", minAmount);

  if (tokenAddress !== undefined) {
    // Validate token address format (basic validation)
    if (!tokenAddress.match(/^[a-z0-9-]+$/)) {
      const msg = await client.createTextMessage(
        "❌ Invalid token address format. Please provide a valid canister ID."
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Use minAmount directly from client.decimalArg() or default to 0
    const finalMinAmount = minAmount || 0;

    console.log("🔍 Debug chatId:", client.chatId, "type:", typeof client.chatId);
    // Extract groupId properly - chatId might be an object with a specific structure
    let groupId: string | undefined;
    if (client.chatId) {
      if (typeof client.chatId === 'string') {
        groupId = client.chatId;
      } else if (typeof client.chatId === 'object') {
        // Check if it's an object with a specific structure
        if ('Group' in client.chatId && client.chatId.Group) {
          groupId = String(client.chatId.Group);
        } else if ('group' in client.chatId && client.chatId.group) {
          groupId = String(client.chatId.group);
        } else if ('id' in client.chatId && client.chatId.id) {
          groupId = String(client.chatId.id);
        } else {
          // Try to extract any string-like property
          const chatIdObj = client.chatId as any;
          const keys = Object.keys(chatIdObj);
          if (keys.length > 0) {
            groupId = String(chatIdObj[keys[0]]);
          } else {
            groupId = JSON.stringify(client.chatId);
          }
        }
      } else {
        groupId = String(client.chatId);
      }
    }

    const userId = client.initiator;
    console.log("🔍 Debug groupId:", groupId, "userId:", userId);

    if (!groupId || !userId) {
      const msg = await client.createTextMessage("❌ Unable to identify group or user.");
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Get or create group config
    let groupConfig = await storageService.getGroupConfig(groupId);
    if (!groupConfig) {
      groupConfig = {
        groupId,
        watchedTokens: [],
        admins: [userId], // First user becomes admin
        isActive: true,
        addedBy: userId,
        createdAt: new Date(),
        lastActivity: new Date()
      };
      await storageService.saveGroupConfig(groupConfig);
    }

    // Check if user is admin (can manage tokens)
    const canManage = await adminService.canManageTokens(groupId, userId);
    if (!canManage) {
      const statusMessage = await adminService.getAdminStatusMessage(groupId, userId);
      const msg = await client.createTextMessage(
        `❌ **Permission Denied**\n\n` +
        `Only group admins can register tokens.\n\n` +
        `**Your Status:** ${statusMessage}\n\n` +
        `Contact a group admin to register tokens or to be granted admin permissions.`
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Check if token is already being watched
    const existingToken = groupConfig.watchedTokens.find(
      t => t.tokenAddress.toLowerCase() === tokenAddress.toLowerCase()
    );

    if (existingToken) {
      const msg = await client.createTextMessage(
        `❌ Token \`${tokenAddress}\` is already being watched in this group.\n\n` +
        `**Current minimum amount:** $${existingToken.minAmount}\n\n` +
        `Use \`/unregister ${tokenAddress}\` to remove it first, then register again with new settings.`
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Add token to group
    const tokenConfig = {
      tokenAddress,
      minAmount: finalMinAmount,
      addedBy: userId,
      addedAt: new Date(),
      tokenSymbol: undefined,
      tokenName: undefined
    };

    const success_result = await storageService.addTokenToGroup(groupId, tokenConfig);

    if (success_result) {
      let message = `✅ **Token Registered Successfully!**\n\n`;
      message += `**Token Address:** \`${tokenAddress}\`\n`;
      message += `**Minimum Alert Amount:** $${finalMinAmount}\n`;
      message += `**Group:** ${groupId}\n`;
      message += `**Added By:** ${userId}\n\n`;
      message += `🔔 You will now receive alerts when someone buys this token with a value of $${finalMinAmount} or more.`;

      const msg = await client.createTextMessage(message);
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );

      console.log(`✅ Token ${tokenAddress} registered for group ${groupId} by user ${userId}`);

      // Register the bot client for this group so it can send buy alerts
      messageService.registerBotClient(groupId, client);
    } else {
      const msg = await client.createTextMessage(
        "❌ Failed to register token. Please try again later."
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
    }

  } else {
    res.status(400).send(argumentsInvalid());
  }
}
