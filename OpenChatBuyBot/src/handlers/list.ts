import { Response } from "express";
import { WithBotClient } from "../types";
import { StorageService } from "../services/storage";
import { success } from "./success";

const storageService = StorageService.getInstance();

export default async function list(req: WithBotClient, res: Response) {
  try {
    const client = req.botClient;
    const groupId = client.chatId?.toString();

    if (!groupId) {
      const msg = await client.createTextMessage("❌ Unable to identify group.");
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Get group config
    const groupConfig = await storageService.getGroupConfig(groupId);

    if (!groupConfig || groupConfig.watchedTokens.length === 0) {
      const msg = await client.createTextMessage(
        "📋 **No Tokens Being Watched**\n\n" +
        "This group is not currently watching any tokens.\n\n" +
        "Use `/register <token_address>` to start watching tokens for buy alerts."
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Build the list message
    let message = `📋 **Watched Tokens (${groupConfig.watchedTokens.length})**\n\n`;

    groupConfig.watchedTokens.forEach((token, index) => {
      message += `**${index + 1}.** \`${token.tokenAddress}\`\n`;
      message += `   💰 **Min Alert:** $${token.minAmount}\n`;
      message += `   👤 **Added By:** ${token.addedBy}\n`;
      message += `   📅 **Added:** ${token.addedAt.toLocaleDateString()}\n\n`;
    });

    message += `**Group ID:** ${groupId}\n`;
    message += `**Total Admins:** ${groupConfig.admins?.length || 0}\n`;
    message += `**Status:** ${groupConfig.isActive ? '🟢 Active' : '🔴 Inactive'}\n\n`;
    message += `Use \`/register <token_address>\` to add more tokens.\n`;
    message += `Use \`/unregister <token_address>\` to remove tokens.`;

    const msg = await client.createTextMessage(message);
    res.status(200).json(success(msg));
    client.sendMessage(msg).catch((err: unknown) =>
      console.error("sendMessage failed with: ", err)
    );

  } catch (error) {
    console.error('❌ Error in list handler:', error);
    const msg = await req.botClient.createTextMessage(
      "❌ An error occurred while retrieving the token list. Please try again later."
    );
    res.status(200).json(success(msg));
    req.botClient.sendMessage(msg).catch((err: unknown) =>
      console.error("sendMessage failed with: ", err)
    );
  }
}
