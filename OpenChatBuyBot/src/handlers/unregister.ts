import { Response } from "express";
import { WithBotClient } from "../types";
import { StorageService } from "../services/storage";
import { AdminService } from "../services/admin";
import { success } from "./success";
import { argumentsInvalid } from "@open-ic/openchat-botclient-ts";

const storageService = StorageService.getInstance();
const adminService = AdminService.getInstance();

export default async function unregister(req: WithBotClient, res: Response) {
  try {
    const client = req.botClient;

    // Extract the token address argument using the correct OpenChat pattern
    const tokenAddress = client.stringArg("token_address");
    if (tokenAddress === undefined) {
      res.status(400).send(argumentsInvalid());
      return;
    }

    // Validate token address format (basic validation)
    if (!tokenAddress || !tokenAddress.match(/^[a-z0-9-]+$/)) {
      const msg = await client.createTextMessage(
        "❌ Invalid token address format. Please provide a valid canister ID."
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    const groupId = client.chatId?.toString();
    const userId = client.initiator;

    if (!groupId || !userId) {
      const msg = await client.createTextMessage("❌ Unable to identify group or user.");
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Get group config
    const groupConfig = await storageService.getGroupConfig(groupId);
    if (!groupConfig) {
      const msg = await client.createTextMessage(
        "❌ No tokens are being watched in this group. Use `/register` to add tokens first."
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Check if user is admin (can manage tokens)
    const canManage = await adminService.canManageTokens(groupId, userId);
    if (!canManage) {
      const statusMessage = await adminService.getAdminStatusMessage(groupId, userId);
      const msg = await client.createTextMessage(
        `❌ **Permission Denied**\n\n` +
        `Only group admins can unregister tokens.\n\n` +
        `**Your Status:** ${statusMessage}\n\n` +
        `Contact a group admin to unregister tokens or to be granted admin permissions.`
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Check if token is being watched
    const tokenExists = groupConfig.watchedTokens.some(
      t => t.tokenAddress.toLowerCase() === tokenAddress.toLowerCase()
    );

    if (!tokenExists) {
      const msg = await client.createTextMessage(
        `❌ Token \`${tokenAddress}\` is not being watched in this group.\n\n` +
        "Use `/list` to see all watched tokens."
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
      return;
    }

    // Remove token from group
    const success_result = await storageService.removeTokenFromGroup(groupId, tokenAddress);

    if (success_result) {
      let message = `✅ **Token Unregistered Successfully!**\n\n`;
      message += `**Token Address:** \`${tokenAddress}\`\n`;
      message += `**Group:** ${groupId}\n`;
      message += `**Removed By:** ${userId}\n\n`;
      message += `🔕 You will no longer receive alerts for this token's trading activity.`;

      const msg = await client.createTextMessage(message);
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );

      console.log(`✅ Token ${tokenAddress} unregistered from group ${groupId} by user ${userId}`);
    } else {
      const msg = await client.createTextMessage(
        "❌ Failed to unregister token. Please try again later."
      );
      res.status(200).json(success(msg));
      client.sendMessage(msg).catch((err: unknown) =>
        console.error("sendMessage failed with: ", err)
      );
    }

  } catch (error) {
    console.error('❌ Error in unregister handler:', error);
    const msg = await req.botClient.createTextMessage(
      "❌ An error occurred while unregistering the token. Please try again later."
    );
    res.status(200).json(success(msg));
    req.botClient.sendMessage(msg).catch((err: unknown) =>
      console.error("sendMessage failed with: ", err)
    );
  }
}
