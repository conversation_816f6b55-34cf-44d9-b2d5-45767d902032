import { commandNotFound } from "@open-ic/openchat-botclient-ts";
import { Request, Response } from "express";
import { WithBotClient } from "../types";
import register from "./register";
import unregister from "./unregister";
import list from "./list";
import status from "./status";
import admin from "./admin";
import help from "./help";

function hasBotClient(req: Request): req is WithBotClient {
  return (req as WithBotClient).botClient !== undefined;
}

export default function executeCommand(req: Request, res: Response): void {
  console.log("🔍 executeCommand function called");

  if (!hasBotClient(req)) {
    console.log("❌ Bot client not initialised");
    res.status(500).send("Bot client not initialised");
    return;
  }
  const client = req.botClient;

  console.log("🎯 Command name:", client.commandName);
  console.log("📋 Command args:", JSON.stringify(client.commandArgs, null, 2));

  switch (client.commandName) {
    case "register":
      register(req, res);
      break;

    case "unregister":
      unregister(req, res);
      break;

    case "list":
      list(req, res);
      break;

    case "status":
      status(req, res);
      break;

    case "admin":
      admin(req, res);
      break;

    case "help":
      help(req, res);
      break;

    default:
      res.status(400).send(commandNotFound());
  }
}
