/**
 * This is an express middleware to create an instance of the OpenChat BotClient
 * which can be used for the duration of a single request to interact with the OpenChat backend.
 * See the readme for more explanation.
 */
import {
  accessTokenNotFound,
  BadRequestError,
  BotClientFactory,
} from "@open-ic/openchat-botclient-ts";
import { NextFunction, Request, Response } from "express";
import { WithBotClient } from "../types";

export function createCommandChatClient(factory: BotClientFactory) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      console.log("🔍 Middleware: createCommandChatClient called");
      const token = req.headers["x-oc-jwt"];
      if (!token) {
        console.log("❌ No JWT token found in headers");
        throw new BadRequestError(accessTokenNotFound());
      }
      console.log("✅ JWT token found, creating bot client");
      (req as WithBotClient).botClient = factory.createClientFromCommandJwt(
        token as string
      );
      console.log("✅ Bot client created successfully");
      console.log("🔍 Calling next() to continue to executeCommand");
      next();
    } catch (err: any) {
      console.log("❌ Error creating bot client: ", err);
      console.log("❌ Error stack:", err.stack);
      if (err instanceof BadRequestError) {
        console.log("❌ Sending 400 response");
        res.status(400).send(err.message);
      } else {
        console.log("❌ Sending 500 response");
        res.status(500).send(err.message);
      }
      return; // Make sure we don't call next() on error
    }
  };
}