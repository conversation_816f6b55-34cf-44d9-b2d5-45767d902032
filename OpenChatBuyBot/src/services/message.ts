/**
 * Enhanced message service for formatting and sending trading notifications to OpenChat groups
 */
import { BuyMessageData } from '../types';
import { BotClientFactory, ChatActionScope, Permissions, GroupChatIdentifier } from '@open-ic/openchat-botclient-ts';

export class MessageService {
  private static instance: MessageService;
  private groupBotClients: Map<string, any> = new Map(); // Store bot clients for different groups
  private botClientFactory: BotClientFactory | null = null;

  private constructor() { }

  public static getInstance(): MessageService {
    if (!MessageService.instance) {
      MessageService.instance = new MessageService();
    }
    return MessageService.instance;
  }

  public setBotClientFactory(factory: BotClientFactory): void {
    this.botClientFactory = factory;
    console.log('MessageService initialized with BotClientFactory');
  }

  public registerBotClient(groupId: string, botClient: any): void {
    this.groupBotClients.set(groupId, botClient);
    console.log(`✅ Registered bot client for group ${groupId}`);
    console.log(`🔍 Total registered clients: ${this.groupBotClients.size}`);
    console.log(`🔍 Registered groups: [${Array.from(this.groupBotClients.keys()).join(', ')}]`);
  }

  public async sendBuyAlert(groupIds: string[], data: BuyMessageData): Promise<void> {
    console.log(`📤 Sending enhanced buy alert for ${data.gotToken.symbol}: $${data.spentDollars.toFixed(2)}`);

    for (const groupId of groupIds) {
      await this.sendAutonomousMessage(groupId, data);
    }
  }

  private async sendAutonomousMessage(groupId: string, data: BuyMessageData): Promise<void> {
    try {
      if (!this.botClientFactory) {
        console.error(`❌ No BotClientFactory available for autonomous messaging`);
        return;
      }

      console.log(`🚀 Sending enhanced autonomous message for ${data.gotToken.symbol} to group ${groupId}`);

      // Create a proper GroupChatIdentifier
      const chatIdentifier = new GroupChatIdentifier(groupId);
      const scope = new ChatActionScope(chatIdentifier);

      // Create permissions for autonomous messaging
      const permissions = Permissions.encodePermissions({
        community: [],
        chat: ["ReadMessages"],
        message: ["Text"]
      });

      // Use the correct API gateway
      const apiGateway = 'lyt4m-myaaa-aaaac-aadkq-cai';

      // Create autonomous client
      const autonomousClient = this.botClientFactory.createClientInAutonomouseContext(
        scope,
        apiGateway,
        new Permissions(permissions)
      );

      // Create enhanced message format with more information
      const enhancedMessage = this.formatEnhancedBuyMessage(data);
      console.log(`💬 Enhanced message: "${enhancedMessage}"`);

      // Create and send message
      const msg = (await autonomousClient.createTextMessage(enhancedMessage)).setBlockLevelMarkdown(true);
      msg.setFinalised(true);

      const result = await autonomousClient.sendMessage(msg);

      // Check if the result indicates success or failure
      if (result && typeof result === 'object' && 'kind' in result) {
        if (result.kind === 'error') {
          console.error(`❌ Enhanced autonomous message failed for group ${groupId}:`, result);
          console.log(`📋 Error details: Code ${result.code} - ${this.getOpenChatErrorMessage(result.code)}`);
        } else {
          console.log(`✅ Enhanced buy alert sent successfully to group ${groupId}`);
        }
      } else {
        console.log(`✅ Enhanced buy alert sent successfully to group ${groupId}`);
      }

    } catch (error) {
      console.error(`❌ Error in enhanced autonomous messaging for group ${groupId}:`, error);
    }
  }

  private formatEnhancedBuyMessage(data: BuyMessageData): string {
    const { spentToken, gotToken, spentDollars, dex, marketcap, holderIncrease } = data;
    const holderWallet = (data as any).holderWallet || (data as any).holderAddress; // Safe access for optional field
    const holderPercentageOwned = (data as any).holderPercentageOwned;

    // Format amounts using BuyBot-style formatting
    const spentAmount = this.formatTokenAmount(spentToken.amount || 0);
    const gotAmount = this.formatTokenAmount(gotToken.amount || 0);
    const spentUSD = this.formatDollarAmount(spentDollars);

    // Calculate token price (USD per token)
    const tokenPrice = (gotToken.amount && gotToken.amount > 0) ? spentDollars / gotToken.amount : 0;
    const priceFormatted = this.formatTokenAmount(tokenPrice);

    // Generate emojis based on trade size (like BuyBot)
    const emojiDenominator = 20;
    const emojiCount = Math.min(Math.floor(spentDollars / emojiDenominator) || 1, 10); // Limit to 10 emojis
    const emojis = '🚀'.repeat(emojiCount);

    // Format holder increase text (like BuyBot)
    const getHolderIncreaseText = (increase: string) => {
      if (!increase) return '';
      if (increase.startsWith('New')) return `**👥 ${increase}**\n`;
      if (increase.startsWith('+')) return `**⏫ Position Increase: ${increase}**\n`;
      return `**📊 ${increase}**\n`;
    };

    // Format holder percentage
    const formatHolderPercentage = (percentage: number | undefined): string => {
      if (!percentage) return '';
      if (percentage < 0.01) return '\[< 0.01%\]';
      return `\[${percentage.toFixed(2)}%\]`;
    };

    // Create holder wallet section
    const holderWalletSection = holderWallet ?
      `**💸[Holder wallet](https://www.icexplorer.io/address/detail/${holderWallet}) ${holderPercentageOwned ? formatHolderPercentage(holderPercentageOwned) : ''}**\n` : '';

    // Create links section (simplified for OpenChat)
    const dexscreenerLink = `https://dexscreener.com/icp/${gotToken.address}`;
    const tokenDetailsLink = `https://app.icpswap.com/info-tokens/details/${gotToken.address}`;

    // Build message in BuyBot style
    const parts = [
      `**__🚨 ${gotToken.name} New Buy!🚨__**\n\n`,
      `${emojis}\n\n`,
      `**💰Spent: ${spentAmount} ${spentToken.symbol} \[$${spentUSD}\]**\n`,
      `**🧳Bought: ${gotAmount} ${gotToken.symbol}**\n`,
      `**💵Price: $${priceFormatted}**\n`,
      holderIncrease ? getHolderIncreaseText(holderIncrease) : '',
      marketcap && marketcap > 0 ? `**📊Marketcap: $${this.formatDollarAmount(marketcap, false)}**\n` : '',
      holderWalletSection,
      `**📈[DexScreener](${dexscreenerLink}) | [Swap](${tokenDetailsLink})**\n`
    ];

    return parts.join('');
  }

  /**
   * Formats a number to show 3 decimal places after the first non-zero digit (BuyBot style)
   */
  private formatTokenAmount(value: number): string {
    if (value === 0) {
      return '0';
    }

    // For numbers 1 or greater, format with thousands separators and up to 3 decimal places.
    if (value >= 1) {
      return value.toLocaleString('en-US', {
        maximumFractionDigits: 3,
      });
    }

    // For numbers less than 1, apply the special formatting rule.
    // Use `toFixed` with a high precision to get a string representation without scientific notation.
    const valueString = value.toFixed(50);

    // Find the index of the first non-zero digit after the decimal point.
    const decimalIndex = valueString.indexOf('.');
    let firstNonZeroIndex = -1;

    for (let i = decimalIndex + 1; i < valueString.length; i++) {
      if (valueString[i] !== '0') {
        firstNonZeroIndex = i;
        break;
      }
    }

    // If for some reason no non-zero digit is found, return 0.
    if (firstNonZeroIndex === -1) {
      return '0';
    }

    // Calculate the total number of decimal places to show:
    // (number of leading zeros) + (the first non-zero digit) + (3 additional digits).
    const leadingZeros = firstNonZeroIndex - decimalIndex - 1;
    const digitsToShow = leadingZeros + 1 + 3;

    // Use toFixed() again to perform rounding and get the final string.
    return value.toFixed(digitsToShow);
  }

  /**
   * Formats a dollar amount with commas for thousands and appropriate decimals (BuyBot style)
   */
  private formatDollarAmount(value: number, decimals: boolean = true): string {
    if (Number.isInteger(value) || decimals === false) {
      return value.toLocaleString('en-US', { maximumFractionDigits: 0 });
    }
    return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }



  private getOpenChatErrorMessage(code: number): string {
    const errorMessages: { [key: number]: string } = {
      101: 'InitiatorNotAuthorized - Bot not authorized for autonomous messaging',
      107: 'Message too long - Content exceeds maximum length',
      287: 'MessageIdAlreadyExists - Message ID collision, trying unique message',
      // Add more error codes as needed
    };

    return errorMessages[code] || `Unknown error code: ${code}`;
  }
}
