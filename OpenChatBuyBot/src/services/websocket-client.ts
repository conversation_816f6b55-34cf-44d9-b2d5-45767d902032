/**
 * WebSocket client to connect to the Fetcher service and receive real-time trading events
 */
import WebSocket from 'ws';
import { BuyMessageData } from '../types';
import { StorageService } from './storage';
import { MessageService } from './message';

export class WebSocketClientService {
  private static instance: WebSocketClientService;
  private ws: WebSocket | null = null;
  private reconnectInterval: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectDelay = 5000; // 5 seconds
  private isConnecting = false;
  private storageService: StorageService;
  private messageService: MessageService;

  private constructor() {
    this.storageService = StorageService.getInstance();
    this.messageService = MessageService.getInstance();
  }

  public static getInstance(): WebSocketClientService {
    if (!WebSocketClientService.instance) {
      WebSocketClientService.instance = new WebSocketClientService();
    }
    return WebSocketClientService.instance;
  }

  public async connect(url: string = 'ws://localhost:2137'): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      console.log('WebSocket already connecting or connected');
      return;
    }

    this.isConnecting = true;
    console.log(`Attempting to connect to WebSocket at ${url}...`);

    try {
      this.ws = new WebSocket(url);

      this.ws.on('open', () => {
        console.log('✅ Connected to Fetcher WebSocket service');
        this.reconnectAttempts = 0;
        this.isConnecting = false;

        if (this.reconnectInterval) {
          clearInterval(this.reconnectInterval);
          this.reconnectInterval = null;
        }
      });

      this.ws.on('message', async (data: WebSocket.Data) => {
        try {
          const message = JSON.parse(data.toString()) as any;
          await this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          console.log('Raw message:', data.toString());
        }
      });

      this.ws.on('close', (code, reason) => {
        console.log(`WebSocket connection closed. Code: ${code}, Reason: ${reason}`);
        this.isConnecting = false;
        this.ws = null;
        this.scheduleReconnect(url);
      });

      this.ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.isConnecting = false;
      });

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.isConnecting = false;
      this.scheduleReconnect(url);
    }
  }

  private scheduleReconnect(url: string): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`Max reconnection attempts (${this.maxReconnectAttempts}) reached. Stopping reconnection.`);
      return;
    }

    if (this.reconnectInterval) {
      return; // Already scheduled
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, Math.min(this.reconnectAttempts - 1, 5)); // Exponential backoff, max 5

    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

    this.reconnectInterval = setTimeout(() => {
      this.reconnectInterval = null;
      this.connect(url);
    }, delay);
  }

  private async handleMessage(message: any): Promise<void> {
    try {
      console.log('📨 Received WebSocket message:', {
        type: message.type,
        hasData: !!message.data || !!message
      });

      // Handle different message formats
      let messageData: BuyMessageData;
      let messageType: string;

      if (message.type) {
        // New format with type field
        messageType = message.type;
        messageData = message.data || message;
      } else {
        // Legacy format - determine type from message content
        if (message.spentToken && message.gotToken) {
          messageType = 'buy';
          messageData = message as BuyMessageData;
        } else {
          console.warn('Unknown message format - only buy messages are supported:', message);
          return;
        }
      }

      // Process only buy messages
      switch (messageType) {
        case 'buy':
        case 'NewBuy':
          await this.handleBuyMessage(messageData as BuyMessageData);
          break;
        default:
          console.log(`Ignoring message type '${messageType}' - only buy messages are processed`);
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
    }
  }

  private async handleBuyMessage(data: BuyMessageData): Promise<void> {
    console.log('🔄 Processing buy message for token:', data.gotToken.symbol);

    // Find groups watching this token
    const watchingGroups = await this.storageService.getGroupsWatchingToken(data.gotToken.address);

    if (watchingGroups.length === 0) {
      console.log(`No groups watching token ${data.gotToken.symbol} (${data.gotToken.address})`);
      return;
    }

    console.log(`Found ${watchingGroups.length} groups watching ${data.gotToken.symbol}`);

    // Send message to each watching group
    for (const group of watchingGroups) {
      try {
        // Check if this trade meets the group's criteria
        const tokenConfig = group.watchedTokens.find(
          t => t.tokenAddress.toLowerCase() === data.gotToken.address.toLowerCase()
        );

        if (tokenConfig && tokenConfig.minAmount && data.spentDollars < tokenConfig.minAmount) {
          console.log(`Trade amount $${data.spentDollars} below minimum $${tokenConfig.minAmount} for group ${group.groupId}`);
          continue;
        }

        await this.messageService.sendBuyAlert([group.groupId], data);
        await this.storageService.updateGroupActivity(group.groupId);
      } catch (error) {
        console.error(`Error sending buy message to group ${group.groupId}:`, error);
      }
    }
  }



  public disconnect(): void {
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
      this.reconnectInterval = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.reconnectAttempts = 0;
    this.isConnecting = false;
    console.log('WebSocket client disconnected');
  }

  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  public getConnectionStatus(): string {
    if (!this.ws) return 'disconnected';

    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'connected';
      case WebSocket.CLOSING: return 'closing';
      case WebSocket.CLOSED: return 'closed';
      default: return 'unknown';
    }
  }
}
