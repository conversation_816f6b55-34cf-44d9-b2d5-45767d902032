/**
 * Simple in-memory storage service for group configurations and token watchlists
 * In a production environment, this should be replaced with a proper database
 */
import { GroupConfig, TokenWatchConfig } from '../types';

export class StorageService {
  private static instance: StorageService;
  private groupConfigs: Map<string, GroupConfig> = new Map();

  private constructor() { }

  public static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService();
    }
    return StorageService.instance;
  }

  // Group management
  public async getGroupConfig(groupId: string): Promise<GroupConfig | null> {
    return this.groupConfigs.get(groupId) || null;
  }

  public async getAllGroupConfigs(): Promise<GroupConfig[]> {
    return Array.from(this.groupConfigs.values());
  }

  public async saveGroupConfig(config: GroupConfig): Promise<void> {
    // Ensure admins array exists
    if (!config.admins) {
      config.admins = [];
    }
    this.groupConfigs.set(config.groupId, config);
    console.log(`Saved group config for ${config.groupId}:`, config);
  }

  public async deleteGroupConfig(groupId: string): Promise<boolean> {
    const deleted = this.groupConfigs.delete(groupId);
    if (deleted) {
      console.log(`Deleted group config for ${groupId}`);
    }
    return deleted;
  }

  // Token management
  public async addTokenToGroup(groupId: string, tokenConfig: TokenWatchConfig): Promise<boolean> {
    const groupConfig = await this.getGroupConfig(groupId);
    if (!groupConfig) {
      return false;
    }

    // Check if token already exists
    const existingIndex = groupConfig.watchedTokens.findIndex(
      t => t.tokenAddress.toLowerCase() === tokenConfig.tokenAddress.toLowerCase()
    );

    if (existingIndex >= 0) {
      // Update existing token config
      groupConfig.watchedTokens[existingIndex] = tokenConfig;
    } else {
      // Add new token
      groupConfig.watchedTokens.push(tokenConfig);
    }

    groupConfig.lastActivity = new Date();
    await this.saveGroupConfig(groupConfig);
    return true;
  }

  public async removeTokenFromGroup(groupId: string, tokenAddress: string): Promise<boolean> {
    const groupConfig = await this.getGroupConfig(groupId);
    if (!groupConfig) {
      return false;
    }

    const initialLength = groupConfig.watchedTokens.length;
    groupConfig.watchedTokens = groupConfig.watchedTokens.filter(
      t => t.tokenAddress.toLowerCase() !== tokenAddress.toLowerCase()
    );

    if (groupConfig.watchedTokens.length < initialLength) {
      groupConfig.lastActivity = new Date();
      await this.saveGroupConfig(groupConfig);
      return true;
    }

    return false;
  }

  public async getTokensForGroup(groupId: string): Promise<TokenWatchConfig[]> {
    const groupConfig = await this.getGroupConfig(groupId);
    return groupConfig?.watchedTokens || [];
  }

  // Find groups watching a specific token
  public async getGroupsWatchingToken(tokenAddress: string): Promise<GroupConfig[]> {
    const allGroups = await this.getAllGroupConfigs();
    return allGroups.filter(group =>
      group.isActive &&
      group.watchedTokens.some(token =>
        token.tokenAddress.toLowerCase() === tokenAddress.toLowerCase()
      )
    );
  }

  // Get all unique tokens being watched across all groups
  public async getAllWatchedTokens(): Promise<string[]> {
    const allGroups = await this.getAllGroupConfigs();
    const tokenSet = new Set<string>();

    allGroups.forEach(group => {
      if (group.isActive) {
        group.watchedTokens.forEach(token => {
          tokenSet.add(token.tokenAddress.toLowerCase());
        });
      }
    });

    return Array.from(tokenSet);
  }

  // Update group activity timestamp
  public async updateGroupActivity(groupId: string): Promise<void> {
    const groupConfig = await this.getGroupConfig(groupId);
    if (groupConfig) {
      groupConfig.lastActivity = new Date();
      await this.saveGroupConfig(groupConfig);
    }
  }

  // Activate/deactivate group
  public async setGroupActive(groupId: string, isActive: boolean): Promise<boolean> {
    const groupConfig = await this.getGroupConfig(groupId);
    if (!groupConfig) {
      return false;
    }

    groupConfig.isActive = isActive;
    groupConfig.lastActivity = new Date();
    await this.saveGroupConfig(groupConfig);
    return true;
  }

  // Get statistics
  public async getStats(): Promise<{
    totalGroups: number;
    activeGroups: number;
    totalTokensWatched: number;
    uniqueTokensWatched: number;
  }> {
    const allGroups = await this.getAllGroupConfigs();
    const activeGroups = allGroups.filter(g => g.isActive);
    const allTokens = await this.getAllWatchedTokens();

    let totalTokensWatched = 0;
    activeGroups.forEach(group => {
      totalTokensWatched += group.watchedTokens.length;
    });

    return {
      totalGroups: allGroups.length,
      activeGroups: activeGroups.length,
      totalTokensWatched,
      uniqueTokensWatched: allTokens.length
    };
  }
}
