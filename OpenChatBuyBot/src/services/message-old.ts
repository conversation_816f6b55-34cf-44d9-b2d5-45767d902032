/**
 * Message service for formatting and sending trading notifications to OpenChat groups
 */
import { BuyMessageData } from '../types';
import { BotClientFactory, ChatActionScope, Permissions, GroupChatIdentifier } from '@open-ic/openchat-botclient-ts';

export class MessageService {
  private static instance: MessageService;
  private groupBotClients: Map<string, any> = new Map(); // Store bot clients for different groups
  private botClientFactory: BotClientFactory | null = null;

  private constructor() { }

  public static getInstance(): MessageService {
    if (!MessageService.instance) {
      MessageService.instance = new MessageService();
    }
    return MessageService.instance;
  }

  // Initialize the bot client factory (called from server startup)
  public initializeBotClientFactory(factory: BotClientFactory): void {
    this.botClientFactory = factory;
    console.log('📝 MessageService initialized with BotClientFactory');
  }

  // Register a bot client for a specific group (called from command handlers)
  public registerBotClient(groupId: string, botClient: any): void {
    this.groupBotClients.set(groupId, botClient);
    console.log(`📝 Registered bot client for group ${groupId}`);
  }

  public async sendBuyMessage(groupId: string, data: BuyMessageData): Promise<void> {
    const formattedMessage = this.formatBuyMessage(data);
    console.log(`📤 Attempting autonomous buy message to group ${groupId}:`, formattedMessage.substring(0, 100) + '...');

    try {
      if (!this.botClientFactory) {
        console.error(`❌ No BotClientFactory available for autonomous messaging`);
        console.log(`[GROUP ${groupId}] BUY ALERT (NO FACTORY):\n${formattedMessage}`);
        return;
      }

      // TODO: Implement autonomous messaging
      // For now, just log the message since we're having TypeScript compilation issues
      console.log(`� Autonomous messaging not yet implemented - logging message:`);
      console.log(`[GROUP ${groupId}] BUY ALERT:\n${formattedMessage}`);

    } catch (error) {
      console.error(`❌ Error in autonomous messaging for group ${groupId}:`, error);
      console.log(`[GROUP ${groupId}] BUY ALERT (ERROR):\n${formattedMessage}`);
    }
  }



  private formatBuyMessage(data: BuyMessageData): string {
    const { spentToken, gotToken, spentDollars, holderIncrease, marketcap, dex, timestamp } = data;

    // Format numbers
    const spentAmount = this.formatNumber(spentToken.amount || 0);
    const gotAmount = this.formatNumber(gotToken.amount || 0);
    const spentUSD = this.formatCurrency(spentDollars);
    const mcap = this.formatCurrency(marketcap);

    // Create emoji indicators
    const dexEmoji = this.getDexEmoji(dex);
    const sizeEmoji = this.getTradeSizeEmoji(spentDollars);

    let message = `${sizeEmoji} **BUY ALERT** ${dexEmoji}\n\n`;
    message += `**Token:** ${gotToken.symbol} (${gotToken.name})\n`;
    message += `**Trade:** ${spentAmount} ${spentToken.symbol} → ${gotAmount} ${gotToken.symbol}\n`;
    message += `**Value:** ${spentUSD}\n`;
    message += `**Market Cap:** ${mcap}\n`;
    message += `**DEX:** ${dex}\n`;

    if (holderIncrease) {
      message += `**Holder Change:** ${holderIncrease}\n`;
    }

    if (data.holderPercentageOwned) {
      message += `**Holder Owns:** ${data.holderPercentageOwned.toFixed(4)}%\n`;
    }

    if (data.icpCollected !== undefined) {
      message += `**ICP Collected:** ${data.icpCollected.toFixed(2)} ICP\n`;
      const bondingProgress = (data.icpCollected / 500) * 100;
      message += `**Bonding Progress:** ${bondingProgress.toFixed(1)}%\n`;
    }

    if (timestamp) {
      message += `**Time:** ${timestamp}\n`;
    }

    message += `\n**Token Address:** \`${gotToken.address}\``;

    return message;
  }



  private formatNumber(num: number): string {
    if (num >= 1e9) {
      return (num / 1e9).toFixed(2) + 'B';
    } else if (num >= 1e6) {
      return (num / 1e6).toFixed(2) + 'M';
    } else if (num >= 1e3) {
      return (num / 1e3).toFixed(2) + 'K';
    } else if (num >= 1) {
      return num.toFixed(2);
    } else if (num >= 0.01) {
      return num.toFixed(4);
    } else {
      return num.toExponential(2);
    }
  }

  private formatCurrency(amount: number): string {
    return '$' + this.formatNumber(amount);
  }

  private getDexEmoji(dex: string): string {
    const dexLower = dex.toLowerCase();
    if (dexLower.includes('icpswap')) return '🔄';
    if (dexLower.includes('kong')) return '🦍';
    if (dexLower.includes('bob')) return '🎪';
    return '💱';
  }

  private getTradeSizeEmoji(amount: number): string {
    if (amount >= 10000) return '🐋'; // Whale
    if (amount >= 1000) return '🦈'; // Shark
    if (amount >= 100) return '🐟'; // Fish
    return '🦐'; // Shrimp
  }

  // Utility method to send a simple text message to a group
  public async sendTextMessage(groupId: string, message: string): Promise<void> {
    console.log(`📤 Sending text message to group ${groupId}:`, message.substring(0, 100) + '...');

    // TODO: Implement actual OpenChat message sending
    console.log(`[GROUP ${groupId}] MESSAGE:\n${message}`);
  }

  // Method to send formatted help message
  public async sendHelpMessage(groupId: string): Promise<void> {
    const helpMessage = `🤖 **OpenChat Buy Bot Help**\n\n` +
      `**Available Commands:**\n` +
      `• \`/register <token_address>\` - Add a token to watch list\n` +
      `• \`/unregister <token_address>\` - Remove a token from watch list\n` +
      `• \`/list\` - Show all watched tokens\n` +
      `• \`/status\` - Show bot status and statistics\n` +
      `• \`/help\` - Show this help message\n\n` +
      `**Features:**\n` +
      `• Real-time buy alerts for watched tokens\n` +
      `• Multi-DEX support (ICPSwap, KongSwap, bob.fun)\n` +
      `• Customizable minimum trade amounts\n` +
      `• Multi-group support\n\n` +
      `**Example:**\n` +
      `\`/register ryjl3-tyaaa-aaaaa-aaaba-cai\``;

    await this.sendTextMessage(groupId, helpMessage);
  }

  // Helper method to decode OpenChat error codes
  private getOpenChatErrorMessage(code: number): string {
    const errorCodes: { [key: number]: string } = {
      287: "Insufficient permissions - Bot may not have permission to send messages to this group",
      401: "Unauthorized - Invalid bot credentials",
      403: "Forbidden - Bot is not a member of this group",
      404: "Not found - Group does not exist",
      429: "Rate limited - Too many requests",
      500: "Internal server error",
    };

    return errorCodes[code] || `Unknown error code ${code}`;
  }
}
