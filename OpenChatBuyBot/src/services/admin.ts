/**
 * Admin service for managing group permissions and admin users
 */
import { StorageService } from './storage';

export class AdminService {
  private static instance: AdminService;
  private storageService: StorageService;

  private constructor() {
    this.storageService = StorageService.getInstance();
  }

  public static getInstance(): AdminService {
    if (!AdminService.instance) {
      AdminService.instance = new AdminService();
    }
    return AdminService.instance;
  }

  /**
   * Check if a user is an admin in a specific group
   */
  public async isGroupAdmin(groupId: string, userId: string): Promise<boolean> {
    const groupConfig = await this.storageService.getGroupConfig(groupId);

    if (!groupConfig) {
      return false;
    }

    // The user who created the group is always an admin
    if (groupConfig.addedBy === userId) {
      return true;
    }

    // Check if user is in the admins list
    return groupConfig.admins?.includes(userId) || false;
  }

  /**
   * Add an admin to a group (only existing admins can do this)
   */
  public async addGroupAdmin(groupId: string, requesterId: string, newAdminId: string): Promise<boolean> {
    // Check if requester is an admin
    if (!(await this.isGroupAdmin(groupId, requesterId))) {
      return false;
    }

    const groupConfig = await this.storageService.getGroupConfig(groupId);
    if (!groupConfig) {
      return false;
    }

    // Initialize admins array if it doesn't exist
    if (!groupConfig.admins) {
      groupConfig.admins = [];
    }

    // Don't add if already an admin
    if (groupConfig.admins.includes(newAdminId)) {
      return true; // Already an admin, consider it success
    }

    groupConfig.admins.push(newAdminId);
    groupConfig.lastActivity = new Date();
    await this.storageService.saveGroupConfig(groupConfig);

    console.log(`✅ Added admin ${newAdminId} to group ${groupId} by ${requesterId}`);
    return true;
  }

  /**
   * Remove an admin from a group (only existing admins can do this, can't remove group creator)
   */
  public async removeGroupAdmin(groupId: string, requesterId: string, adminToRemove: string): Promise<boolean> {
    // Check if requester is an admin
    if (!(await this.isGroupAdmin(groupId, requesterId))) {
      return false;
    }

    const groupConfig = await this.storageService.getGroupConfig(groupId);
    if (!groupConfig) {
      return false;
    }

    // Can't remove the group creator
    if (groupConfig.addedBy === adminToRemove) {
      return false;
    }

    // Initialize admins array if it doesn't exist
    if (!groupConfig.admins) {
      groupConfig.admins = [];
    }

    const initialLength = groupConfig.admins.length;
    groupConfig.admins = groupConfig.admins.filter(admin => admin !== adminToRemove);

    if (groupConfig.admins.length < initialLength) {
      groupConfig.lastActivity = new Date();
      await this.storageService.saveGroupConfig(groupConfig);
      console.log(`✅ Removed admin ${adminToRemove} from group ${groupId} by ${requesterId}`);
      return true;
    }

    return false; // Admin was not in the list
  }

  /**
   * Get list of admins for a group
   */
  public async getGroupAdmins(groupId: string): Promise<string[]> {
    const groupConfig = await this.storageService.getGroupConfig(groupId);
    if (!groupConfig) {
      return [];
    }

    // Include the group creator and all admins
    const allAdmins = [groupConfig.addedBy, ...(groupConfig.admins || [])];
    return [...new Set(allAdmins)]; // Remove duplicates
  }

  /**
   * Initialize a group with default admin settings
   */
  public async initializeGroupAdmins(groupId: string, creatorId: string): Promise<void> {
    const groupConfig = await this.storageService.getGroupConfig(groupId);
    if (groupConfig && !groupConfig.admins) {
      groupConfig.admins = [];
      await this.storageService.saveGroupConfig(groupConfig);
    }
  }

  /**
   * Check if user can manage tokens (is admin)
   */
  public async canManageTokens(groupId: string, userId: string): Promise<boolean> {
    return await this.isGroupAdmin(groupId, userId);
  }

  /**
   * Get admin status message for a user in a group
   */
  public async getAdminStatusMessage(groupId: string, userId: string): Promise<string> {
    const isAdmin = await this.isGroupAdmin(groupId, userId);
    const groupConfig = await this.storageService.getGroupConfig(groupId);

    if (!groupConfig) {
      return "❌ Group not found";
    }

    if (groupConfig.addedBy === userId) {
      return "👑 You are the group creator (admin)";
    } else if (isAdmin) {
      return "⭐ You are a group admin";
    } else {
      return "👤 You are a group member (contact an admin to manage tokens)";
    }
  }

  /**
   * Format admin list for display
   */
  public async formatAdminList(groupId: string): Promise<string> {
    const groupConfig = await this.storageService.getGroupConfig(groupId);
    if (!groupConfig) {
      return "❌ Group not found";
    }

    const allAdmins = await this.getGroupAdmins(groupId);

    if (allAdmins.length === 0) {
      return "No admins found";
    }

    let message = `**Group Admins (${allAdmins.length}):**\n`;
    allAdmins.forEach((admin, index) => {
      const isCreator = admin === groupConfig.addedBy;
      const emoji = isCreator ? "👑" : "⭐";
      const role = isCreator ? " (Creator)" : " (Admin)";
      message += `${index + 1}. ${emoji} \`${admin}\`${role}\n`;
    });

    return message;
  }
}
