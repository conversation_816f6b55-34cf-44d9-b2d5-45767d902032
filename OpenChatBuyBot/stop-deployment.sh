#!/bin/bash
echo "� Stopping OpenChat Buy Bot deployment..."

# Kill bot process
BOT_PID=$(lsof -ti:3002)
if [ ! -z "$BOT_PID" ]; then
    kill $BOT_PID
    echo "✅ Stopped bot process (PID: $BOT_PID)"
fi

# Kill cloudflared processes
TUNNEL_PIDS=$(pgrep -f cloudflared)
if [ ! -z "$TUNNEL_PIDS" ]; then
    echo $TUNNEL_PIDS | xargs kill
    echo "✅ Stopped tunnel processes"
fi

echo "🎉 Deployment stopped successfully"
