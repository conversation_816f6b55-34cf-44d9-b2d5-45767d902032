#!/bin/bash

# OpenChat Buy Bot Deployment Status Checker
echo "🔍 OpenChat Buy Bot Deployment Status"
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if bot is running on port 3002
echo "🔍 Checking bot process..."
BOT_PID=$(lsof -ti:3002 2>/dev/null)
if [ ! -z "$BOT_PID" ]; then
    print_status "Bot is running (PID: $BOT_PID)"
    
    # Test health endpoint
    if curl -f http://localhost:3002/health > /dev/null 2>&1; then
        print_status "Bot health check passed"
    else
        print_error "Bot health check failed"
    fi
else
    print_error "Bot is not running on port 3002"
fi

# Check if cloudflared is running
echo ""
echo "🔍 Checking Cloudflare tunnel..."
TUNNEL_PIDS=$(pgrep -f cloudflared)
if [ ! -z "$TUNNEL_PIDS" ]; then
    print_status "Cloudflare tunnel is running (PIDs: $TUNNEL_PIDS)"
    
    # Try to extract tunnel URL
    TUNNEL_URL=$(ps aux | grep cloudflared | grep -o 'https://[^[:space:]]*\.trycloudflare\.com' | head -1)
    if [ ! -z "$TUNNEL_URL" ]; then
        print_status "Tunnel URL: $TUNNEL_URL"
        
        # Test tunnel endpoint
        if curl -f "$TUNNEL_URL/health" > /dev/null 2>&1; then
            print_status "Tunnel connectivity test passed"
        else
            print_warning "Tunnel connectivity test failed"
        fi
    else
        print_warning "Could not detect tunnel URL"
    fi
else
    print_error "Cloudflare tunnel is not running"
fi

# Check PM2 status if available
echo ""
echo "🔍 Checking PM2 status..."
if command -v pm2 &> /dev/null; then
    PM2_STATUS=$(pm2 jlist 2>/dev/null)
    if [ $? -eq 0 ]; then
        BOT_STATUS=$(echo $PM2_STATUS | jq -r '.[] | select(.name=="openchat-buy-bot") | .pm2_env.status' 2>/dev/null)
        TUNNEL_STATUS=$(echo $PM2_STATUS | jq -r '.[] | select(.name=="cloudflare-tunnel") | .pm2_env.status' 2>/dev/null)
        
        if [ "$BOT_STATUS" = "online" ]; then
            print_status "PM2 Bot status: online"
        elif [ ! -z "$BOT_STATUS" ]; then
            print_warning "PM2 Bot status: $BOT_STATUS"
        else
            print_info "Bot not managed by PM2"
        fi
        
        if [ "$TUNNEL_STATUS" = "online" ]; then
            print_status "PM2 Tunnel status: online"
        elif [ ! -z "$TUNNEL_STATUS" ]; then
            print_warning "PM2 Tunnel status: $TUNNEL_STATUS"
        else
            print_info "Tunnel not managed by PM2"
        fi
    else
        print_info "No PM2 processes found"
    fi
else
    print_info "PM2 not installed"
fi

# Check environment variables
echo ""
echo "🔍 Checking environment configuration..."
if [ -f .env ]; then
    source .env
    
    if [ ! -z "$IDENTITY_PRIVATE" ]; then
        print_status "IDENTITY_PRIVATE is set"
    else
        print_error "IDENTITY_PRIVATE is not set"
    fi
    
    if [ ! -z "$OC_PUBLIC" ]; then
        print_status "OC_PUBLIC is set"
    else
        print_error "OC_PUBLIC is not set"
    fi
    
    if [ ! -z "$IC_HOST" ]; then
        print_status "IC_HOST is set ($IC_HOST)"
    else
        print_error "IC_HOST is not set"
    fi
    
    if [ ! -z "$STORAGE_INDEX_CANISTER" ]; then
        print_status "STORAGE_INDEX_CANISTER is set ($STORAGE_INDEX_CANISTER)"
    else
        print_error "STORAGE_INDEX_CANISTER is not set"
    fi
    
    if [ ! -z "$WEBSOCKET_URL" ]; then
        print_status "WEBSOCKET_URL is set ($WEBSOCKET_URL)"
    else
        print_warning "WEBSOCKET_URL is not set (using default)"
    fi
else
    print_error ".env file not found"
fi

# Check if build exists
echo ""
echo "🔍 Checking build status..."
if [ -d "dist" ] && [ -f "dist/server.js" ]; then
    print_status "Build directory exists"
    BUILD_DATE=$(stat -c %y dist/server.js 2>/dev/null || stat -f %Sm dist/server.js 2>/dev/null)
    if [ ! -z "$BUILD_DATE" ]; then
        print_info "Last build: $BUILD_DATE"
    fi
else
    print_error "Build directory missing - run 'npm run build'"
fi

# Check dependencies
echo ""
echo "🔍 Checking dependencies..."
if [ -d "node_modules" ]; then
    print_status "Dependencies installed"
else
    print_error "Dependencies not installed - run 'npm install'"
fi

# Summary
echo ""
echo "📊 Deployment Summary"
echo "===================="

if [ ! -z "$BOT_PID" ] && [ ! -z "$TUNNEL_PIDS" ]; then
    print_status "Deployment is RUNNING"
    if [ ! -z "$TUNNEL_URL" ]; then
        echo ""
        echo "🔗 Your bot endpoint: $TUNNEL_URL"
        echo "📋 Use this URL to register your bot in OpenChat"
    fi
else
    print_warning "Deployment is INCOMPLETE"
    echo ""
    echo "🚀 To start deployment:"
    echo "   ./deploy.sh              # Development deployment"
    echo "   ./deploy-production.sh   # Production deployment with PM2"
fi

echo ""
echo "🔧 Useful commands:"
echo "   ./check-deployment.sh    # Check status (this script)"
echo "   ./deploy.sh              # Start development deployment"
echo "   ./deploy-production.sh   # Start production deployment"
echo "   ./stop-deployment.sh     # Stop development deployment"
echo "   ./stop-production.sh     # Stop production deployment"
