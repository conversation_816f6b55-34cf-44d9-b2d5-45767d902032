import { Request, Response } from 'express';
import { ICPService } from '../services/icp/icp';
import { TokenHoldersCalculator } from '../services/icp/holders/calculator';
import { HttpAgent } from '@dfinity/agent';
import { Principal } from '@dfinity/principal';
import { BobFetcher } from '../services/icp/bobFetcher';
import { TokenData } from '../services/icp/IDL/launchBob';
import { getTokenPrice as getTokenPriceFromDB, getTokenMetricsById } from '../services/database/db';
const icpService = new ICPService();

export const getTokensByAddress = async (req: Request, res: Response): Promise<void> => {
  try {
    const { address } = req.params;

    // Validate address format
    if (!address || !address.match(/^[a-zA-Z0-9-_]+$/)) {
      res.status(400).json({
        error: 'Invalid Address',
        message: 'The provided address is not a valid ICP canister ID'
      });
      return;
    }

    const tokenInfo = await icpService.getTokenInfo(address);

    res.json(tokenInfo);
  } catch (error) {
    console.error('Error fetching token data:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Error fetching token data'
    });
  }
};

export const getTokenPools = async (req: Request, res: Response): Promise<void> => {
  try {
    const { address } = req.params;

    // Validate address format
    if (!address || !address.match(/^[a-zA-Z0-9-_]+$/)) {
      res.status(400).json({
        error: 'Invalid Address',
        message: 'The provided address is not a valid ICP canister ID'
      });
      return;
    }

    const pools = await icpService.getPoolAddresses(address);
    res.json(pools);
  } catch (error) {
    console.error('Error fetching token pools:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Error fetching token pools'
    });
  }
};

export const getTokenHolderBalances = async (req: Request, res: Response): Promise<void> => {
  try {
    const { address: canisterId } = req.params;

    if (!canisterId) {
      res.status(400).json({
        error: 'Missing Canister ID',
        message: 'The canisterId path parameter is required.'
      });
      return;
    }

    let principal: Principal;
    try {
      principal = Principal.fromText(canisterId);
    } catch (error) {
      res.status(400).json({
        error: 'Invalid Canister ID',
        message: 'The provided canisterId is not a valid Principal.'
      });
      return;
    }

    // Check for a query parameter that indicates this request comes from button navigation
    const isButtonNavigation = req.query.button === 'true';

    // Process query parameters
    const forceRefreshParam = req.query.refresh?.toString().toLowerCase();
    const forceRefresh = isButtonNavigation ? true : (forceRefreshParam === 'true' || forceRefreshParam === '1');

    // Get maxAge parameter (in minutes) with a default of 1 minute
    const maxAgeParam = req.query.maxAge?.toString();
    const maxAge = maxAgeParam ? parseInt(maxAgeParam, 10) : 1;

    // Configure the host based on environment variables or default to icp-api.io
    const host = process.env.ICP_HOST || 'https://icp-api.io';
    const agent = new HttpAgent({ host });

    console.log(`Retrieving token holder balances for ${canisterId} (forceRefresh: ${forceRefresh}, maxAge: ${maxAge}min)`);
    // Create the calculator with resetActiveCalculations=true when force refreshing to clear any stale calculations
    const calculator = new TokenHoldersCalculator(principal, agent, forceRefresh);

    // If we get here, either this isn't button navigation or we didn't have DB data
    // Use our caching method with optional force refresh
    const balancesMap = await calculator.getHolderBalancesWithCache(forceRefresh, maxAge);

    // Convert map to array of [accountId, balance] pairs
    let sortedBalances = Array.from(balancesMap.entries());

    // Sort by balance (bigint) in descending order
    sortedBalances.sort((a, b) => {
      if (a[1] < b[1]) return 1;
      if (a[1] > b[1]) return -1;
      return 0;
    });

    // Handle optional limit query parameter
    const limitParam = req.query.limit as string | undefined;
    if (limitParam) {
      const limit = parseInt(limitParam, 10);
      if (!isNaN(limit) && limit > 0) {
        sortedBalances = sortedBalances.slice(0, limit);
      } else if (isNaN(limit) || limit <= 0) {
        // Optional: return a 400 error for invalid limit, or just ignore
        // For now, ignoring invalid limit values
        // res.status(400).json({ error: 'Invalid Limit Parameter', message: 'Limit must be a positive integer.' });
        // return;
      }
    }

    // Convert sorted (and possibly limited) array to object for JSON response
    const balancesObject: Record<string, string> = {};
    for (const [key, value] of sortedBalances) {
      balancesObject[key] = value.toString();
    }

    res.json(balancesObject);
  } catch (error) {
    console.error('Error fetching token holder balances:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    res.status(500).json({
      error: 'Internal Server Error',
      message: `Error fetching token holder balances: ${errorMessage}`
    });
  }
};

export const getBobLaunchTokenById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    console.log("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
    console.log(id)
    // Validate address format
    if (!id) {
      res.status(400).json({
        error: 'Invalid Address',
        message: 'The provided address is not a valid ICP canister ID'
      });
      return;
    }
    const fetcher = new BobFetcher();
    const tokenData = await fetcher.getTokenData(BigInt(id));

    // Manually stringify the response to handle BigInt values using a replacer.
    const jsonString = JSON.stringify(tokenData, (key, value) => {
      return typeof value === 'bigint' ? value.toString() : value;
    });

    res.setHeader('Content-Type', 'application/json');
    res.send(jsonString);
  } catch (error) {
    console.error('Error fetching token data:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Error fetching token data'
    });
  }
};

export const getTokenPrice = async (req: Request, res: Response): Promise<void> => {
  try {
    const { address } = req.params;

    // Validate address format
    if (!address || !address.match(/^[a-zA-Z0-9-_]+$/)) {
      res.status(400).json({
        error: 'Invalid Address',
        message: 'The provided address is not a valid ICP canister ID'
      });
      return;
    }

    const price = await getTokenPriceFromDB(address);

    if (price === undefined || price === null) {
      res.status(404).json({
        error: 'Price Not Found',
        message: 'No price data available for this token'
      });
      return;
    }

    res.json({
      address,
      price_usd: price
    });
  } catch (error) {
    console.error('Error fetching token price:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Error fetching token price'
    });
  }
};

export const getTokenMetrics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { address } = req.params;

    // Validate address format
    if (!address || !address.match(/^[a-zA-Z0-9-_]+$/)) {
      res.status(400).json({
        error: 'Invalid Address',
        message: 'The provided address is not a valid ICP canister ID'
      });
      return;
    }

    const metrics = await getTokenMetricsById(address);

    if (!metrics) {
      res.status(404).json({
        error: 'Metrics Not Found',
        message: 'No metrics data available for this token'
      });
      return;
    }

    res.json(metrics);
  } catch (error) {
    console.error('Error fetching token metrics:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Error fetching token metrics'
    });
  }
};