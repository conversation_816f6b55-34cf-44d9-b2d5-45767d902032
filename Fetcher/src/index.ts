import express from 'express';
import cors from 'cors';
import swaggerUi from 'swagger-ui-express';
import { tokensRouter } from './routes/tokens';
import { swaggerDocument } from './swagger';
import { ICPService } from './services/icp/icp';

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize services
const icpService = new ICPService();

// Middleware
app.use(cors());
app.use(express.json());

// Swagger documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

// Routes
app.use('/api/tokens', tokensRouter);

// Health check endpoint
app.get('/health', (_: express.Request, res: express.Response) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.listen(PORT, async () => {
  console.log(`Server is running on port ${PORT}`);
  console.log(`API Documentation available at http://localhost:${PORT}/api-docs`);
  
  // Start pool data fetching (default 5 minutes interval)
  try {
    await icpService.start();
  } catch (error) {
    console.error('Failed to start ICP services:', error);
  }
});

// Handle graceful shutdown
const shutdown = async () => {
  console.log('Shutdown signal received. Cleaning up...');
  try {
    icpService.stop();
    console.log('Cleanup completed successfully');
  } catch (error) {
    console.error('Error during cleanup:', error);
  }
  process.exit(0);
};

process.on('SIGTERM', shutdown);
process.on('SIGINT', shutdown);
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});