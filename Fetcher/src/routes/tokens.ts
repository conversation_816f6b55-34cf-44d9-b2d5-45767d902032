import express from 'express';
import { getTokensByAddress, getTokenPools, getTokenHolderBalances, getBobLaunchTokenById, getTokenPrice, getTokenMetrics } from '../controllers/tokens';

const router = express.Router();

router.get('/:address', getTokensByAddress);
router.get('/:address/pools', getTokenPools);
router.get('/:address/balances', getTokenHolderBalances);
router.get('/:address/price', getTokenPrice);
router.get('/:address/metrics', getTokenMetrics);
router.get('/bob/:id', getBobLaunchTokenById);
export const tokensRouter = router;
