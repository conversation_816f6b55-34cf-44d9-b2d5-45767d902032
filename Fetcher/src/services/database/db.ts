import { Pool } from 'pg';
import { PoolData } from '../../types/icpSwap';
import dotenv from 'dotenv';
import { TokenMetrics, TrendingMetric, RobertChoiceMessageData, RobertTokenInfo } from '../../types';
import path from 'path';
import fs from 'fs';

// Try multiple potential .env file locations
const potentialPaths = [
  path.resolve(process.cwd(), '.env'),
  path.resolve(__dirname, '../../../.env'),
  path.resolve(__dirname, '../../../../.env')
];

let envLoaded = false;
for (const envPath of potentialPaths) {
  if (fs.existsSync(envPath)) {
    console.log(`Found .env file at: ${envPath}`);
    dotenv.config({ path: envPath });
    envLoaded = true;
    break;
  } else {
    console.log(`No .env file found at: ${envPath}`);
  }
}

if (!envLoaded) {
  console.warn('No .env file found in any of the expected locations!');
}

// Log environment variables to debug
console.log('Database connection environment variables:');
console.log(`PGHOST: ${process.env.PGHOST}`);
console.log(`PGDATABASE: ${process.env.PGDATABASE}`);
console.log(`PGUSER: ${process.env.PGUSER}`);
console.log(`PGPORT: ${process.env.PGPORT}`);

let pool: Pool;
let isPoolEnded = false;

function createPool() {
  console.log('Creating new database connection pool');

  // Use environment variables with fallbacks
  const newPool = new Pool({
    user: process.env.PGUSER || "postgres",
    host: process.env.PGHOST || "localhost",
    database: process.env.PGDATABASE || "postgres",
    password: process.env.PGPASSWORD || "password",
    port: Number(process.env.PGPORT) || 5432,
    connectionTimeoutMillis: 30000,  // 30s timeout is a good balance
    idleTimeoutMillis: 60000,        // 60s idle timeout to free up connections
    max: 20,                         // 20 max connections to avoid overloading DB
    min: 3,                          // Always keep some connections ready
    allowExitOnIdle: false,          // Don't exit on idle
    keepAlive: true,                 // Keep connections alive
    keepAliveInitialDelayMillis: 5000,
    statement_timeout: 30000,        // 30s statement timeout
    query_timeout: 30000,            // 30s query timeout
  });

  // Handle pool-level errors
  newPool.on('error', (err) => {
    console.error('Unexpected error on idle client:', err);
    // Optionally recreate the pool in case of fatal errors
    recreatePool();
  });

  return newPool;
}

function recreatePool() {
  if (pool) {
    pool.end().catch((err) => {
      console.error('Error ending old pool during recreation:', err);
    });
  }
  pool = createPool();
}

pool = createPool();

const withRetry = async <T>(operation: (pool: Pool) => Promise<T>, retries = 5): Promise<T> => {
  let lastError;

  for (let i = 0; i < retries; i++) {
    try {
      if (isPoolEnded || i > 0) {
        console.log(`Database connection retry attempt ${i + 1}/${retries} - recreating pool...`);
        if (pool) {
          try {
            await pool.end().catch(() => { });
          } catch (e) {
            console.log('Error while ending pool (can be ignored):', e);
          }
        }
        pool = createPool();
        isPoolEnded = false;
      }

      // Try to get a connection from the pool to test if we're reconnecting
      if (i > 0) {
        try {
          const client = await pool.connect();
          client.release();
          console.log('Successfully reconnected to database');
        } catch (connError: any) {
          console.error('Failed to connect during test:', connError.message);
          throw connError; // Rethrow to trigger retry
        }
      }

      // Pass the valid pool to the operation
      return await operation(pool);
    } catch (error: any) {
      console.error(`Operation failed (attempt ${i + 1}/${retries}):`, error.message);
      lastError = error;

      // Check if this is a connection-related error that should be retried
      const retryableError =
        error.message.includes('timeout exceeded') ||
        error.message.includes('Cannot use a pool after calling end on the pool') ||
        error.code === 'ECONNREFUSED' ||
        error.code === 'PROTOCOL_CONNECTION_LOST' ||
        error.code === '57P01' || // admin shutdown
        error.code === '57P02' || // crash shutdown
        error.code === '57P03' || // cannot connect now
        error.code === '08006' || // connection failure
        error.code === '08001' || // unable to connect
        error.code === '08004' || // rejected connection
        error.code === 'ECONNRESET' ||
        error.message.includes('Connection terminated');

      if (retryableError) {
        if (!isPoolEnded) {
          isPoolEnded = true;
          try {
            await pool.end();
          } catch (endError) {
            console.error('Error ending pool:', endError);
          }
        }

        // Exponential backoff with jitter to avoid thundering herd
        const baseDelay = Math.min(1000 * Math.pow(2, i), 10000);
        const jitter = Math.floor(Math.random() * 1000); // Add up to 1s of jitter
        const delay = baseDelay + jitter;
        console.log(`Retrying in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
        continue;
      }

      // If we get here, it's not a connection-related error
      console.error('Non-retryable error:', error.message);
      throw error;
    }
  }

  console.error('Failed after all retries');
  throw lastError;
};

export async function syncICPSwapPoolsToDb(pools: PoolData[]): Promise<any> {
  return withRetry(async (pool) => {
    const client = await pool.connect();
    const result = [];
    try {
      await client.query('BEGIN');

      for (const pool of pools) {
        const query = `
          INSERT INTO pools (
            key,
            fee,
            tick_spacing,
            canister_id,
            token0_address,
            token0_standard,
            token1_address,
            token1_standard
          ) 
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          ON CONFLICT (key) 
          DO NOTHING
          RETURNING token0_address, token1_address, canister_id
        `;

        const values = [
          pool.key,
          pool.fee.toString(),
          pool.tickSpacing.toString(),
          pool.canisterId.toString(),
          pool.token0.address,
          pool.token0.standard,
          pool.token1.address,
          pool.token1.standard,
        ];
        const res = await client.query(query, values);
        if (res.rows.length === 0) {
          continue;
        }
        console.log("New pool found!")
        result.push(res.rows[0]);
      }
      console.log('Synced pools to db successfully', result);
      await client.query('COMMIT');
    } catch (error) {
      console.error('Error during sync pools to db:', error);
      await client.query('ROLLBACK').catch(rollbackError => {
        console.error('Error during rollback:', rollbackError);
      });
      throw error;
    } finally {
      client.release();
      console.log('Synced pools to db successfully', result);
      return result
    }
  });
}

export async function getAllICPSwapPools(): Promise<PoolData[]> {
  return withRetry(async (pool) => {
    const query = 'SELECT * FROM pools ORDER BY created_at DESC';
    const { rows } = await pool.query(query);

    return rows.map(row => ({
      key: row.key,
      fee: row.fee,
      tickSpacing: row.tick_spacing,
      canisterId: row.canister_id,
      token0: {
        address: row.token0_address,
        standard: row.token0_standard,
      },
      token1: {
        address: row.token1_address,
        standard: row.token1_standard,
      },
    }));
  });
}

export async function getICPSwapPoolsByToken(tokenCanisterId: string): Promise<PoolData[]> {
  return withRetry(async (pool) => {
    const query = `
      SELECT * FROM pools 
      WHERE token0_address = $1 
      OR token1_address = $1 
      ORDER BY created_at DESC
    `;

    const { rows } = await pool.query(query, [tokenCanisterId]);

    return rows.map((row: any) => ({
      key: row.key,
      fee: row.fee,
      tickSpacing: row.tick_spacing,
      canisterId: row.canister_id,
      token0: {
        address: row.token0_address,
        standard: row.token0_standard,
      },
      token1: {
        address: row.token1_address,
        standard: row.token1_standard,
      },
    }));
  });
}

export async function upsertToken(token: {
  address: string;
  decimals: number;
  name: string;
  symbol: string;
  totalSupply: string;
  standard: string;
  owner: string;
}): Promise<void> {
  return withRetry(async (pool) => {
    const query = `
      INSERT INTO tokens (
        address,
        decimals,
        name,
        symbol,
        total_supply,
        standard,
        owner
      ) 
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (address) 
      DO UPDATE SET
        total_supply = $5,
        updated_at = CURRENT_TIMESTAMP
    `;

    const values = [
      token.address,
      Number(token.decimals),
      token.name,
      token.symbol,
      token.totalSupply,
      token.standard,
      token.owner
    ];

    await pool.query(query, values);
  });
}

export async function getTokenInfo(address: string): Promise<any> {
  return withRetry(async (pool) => {
    const tokenQuery = 'SELECT *, updated_at::text as timestamp FROM tokens WHERE address = $1';
    const poolsQuery = `
      SELECT * FROM pools 
      WHERE token0_address = $1 
      OR token1_address = $1 
      ORDER BY created_at DESC
    `;

    const tokenResult = await pool.query(tokenQuery, [address]);
    if (tokenResult.rows.length === 0) {
      return null;
    }

    const token = tokenResult.rows[0];
    const poolsResult = await pool.query(poolsQuery, [address]);

    return {
      address: token.address,
      name: token.name,
      symbol: token.symbol,
      decimals: token.decimals,
      totalSupply: token.total_supply,
      standard: token.standard,
      owner: token.owner,
      poolAddresses: poolsResult.rows.map(pool => pool.canister_id),
      timestamp: token.timestamp
    };
  });
}

// export async function updateTokenTotalSupply(address: string, totalSupply: string): Promise<void> {
//   return withRetry(async (pool) => {
//     const client = await pool.connect();
//     try {
//       const query = `
//         UPDATE tokens 
//         SET total_supply = $2, updated_at = CURRENT_TIMESTAMP
//         WHERE address = $1
//       `;
//       await client.query(query, [address, totalSupply]);
//     } finally {
//       client.release();
//     }
//   });
// }

export async function getAllTokenAddresses(): Promise<string[]> {
  return withRetry(async (pool) => {
    const query = 'SELECT address FROM tokens';
    const { rows } = await pool.query(query);
    return rows.map(row => row.address);
  });
}

export async function updateTokenMetricsDb(tokenMetricsData: TokenMetrics[]): Promise<any> {
  return withRetry(async (pool) => {
    const client = await pool.connect();
    const result = [];
    try {
      await client.query('BEGIN');

      for (const metrics of tokenMetricsData) {
        const checkTokenQuery = `
        SELECT address FROM tokens WHERE address = $1
      `;
        const tokenExists = await client.query(checkTokenQuery, [metrics.address]);

        if (tokenExists.rows.length === 0) {
          // skip if not exists TODO change it in future to have all tokens in db
          continue;
        }
        const query = `
          INSERT INTO token_metrics (
            address,
            volume_usd_1d,
            volume_usd_7d,
            total_volume_usd,
            volume_usd,
            fees_usd,
            price_usd_change,
            tx_count,
            price_usd
          ) 
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          ON CONFLICT (address) 
          DO UPDATE SET
            volume_usd_1d = EXCLUDED.volume_usd_1d,
            volume_usd_7d = EXCLUDED.volume_usd_7d,
            total_volume_usd = EXCLUDED.total_volume_usd,
            volume_usd = EXCLUDED.volume_usd,
            fees_usd = EXCLUDED.fees_usd,
            price_usd_change = EXCLUDED.price_usd_change,
            tx_count = EXCLUDED.tx_count,
            price_usd = EXCLUDED.price_usd,
            updated_at = CURRENT_TIMESTAMP
          RETURNING 1
        `;

        const values = [
          metrics.address,
          metrics.volumeUSD1d,
          metrics.volumeUSD7d,
          metrics.totalVolumeUSD,
          metrics.volumeUSD,
          metrics.feesUSD,
          metrics.priceUSDChange,
          metrics.txCount,
          metrics.priceUSD
        ];

        const res = await client.query(query, values);
        if (res.rows.length > 0) {
          // console.log("Token metrics updated:", metrics.address);
          result.push(res.rows[0]);
        }
      }
      await client.query('COMMIT');
    } catch (error) {
      console.error('Error during token metrics update:', error);
      await client.query('ROLLBACK').catch(rollbackError => {
        console.error('Error during rollback:', rollbackError);
      });
      throw error;
    } finally {
      client.release();
    }
    console.log('Token metrics updated successfully for ', result.length, ' tokens');
    return result;
  });
}

export const getTokenMetrics = async () => {
  return withRetry(async (pool) => {
    const query = `
      SELECT 
        t.address,
        t.name,
        t.symbol,
        tm.volume_usd_1d,
        tm.price_usd,
        tm.price_usd_change,
        gc.socials
      FROM 
        tokens t
      LEFT JOIN 
        token_metrics tm ON t.address = tm.address
      JOIN 
        group_configs gc ON t.address = gc.address AND gc.active = TRUE;
    `;
    const result = await pool.query(query);
    return result.rows;
  });
}

export const updateTrendingMetrics = async (metrics: TrendingMetric[]): Promise<void> => {
  return withRetry(async (pool) => {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      await client.query('DELETE FROM trending'); // Clear the table before inserting new data

      const insertQuery = `
        INSERT INTO trending (address, name, symbol, volume_usd_1d, price_usd, price_usd_change, socials, place, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
        ON CONFLICT (address) DO UPDATE
        SET name = EXCLUDED.name,
            symbol = EXCLUDED.symbol,
            volume_usd_1d = EXCLUDED.volume_usd_1d,
            price_usd = EXCLUDED.price_usd,
            price_usd_change = EXCLUDED.price_usd_change,
            socials = EXCLUDED.socials,
            place = EXCLUDED.place,
            updated_at = CURRENT_TIMESTAMP
      `;

      for (let i = 0; i < metrics.length; i++) {
        const metric = metrics[i];
        await client.query(insertQuery, [
          metric.address,
          metric.name,
          metric.symbol,
          metric.volume_usd_1d,
          metric.price_usd,
          metric.price_usd_change,
          JSON.stringify(metric.socials),
          i + 1 // Place (rank) starts from 1
        ]);
      }

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  });
}

export const getTokenPrice = async (address: string) => {
  return withRetry(async (pool) => {
    const query = `
      SELECT
        price_usd
      FROM
        token_metrics
      WHERE
        address = $1
    `;
    const result = await pool.query(query, [address]);
    return result.rows[0].price_usd;
  });
}

export async function getTokenMetricsById(address: string): Promise<any> {
  return withRetry(async (pool) => {
    const query = `
      SELECT
        address,
        volume_usd_1d,
        volume_usd_7d,
        total_volume_usd,
        volume_usd,
        fees_usd,
        price_usd_change,
        tx_count,
        price_usd,
        updated_at
      FROM
        token_metrics
      WHERE
        address = $1
    `;
    const result = await pool.query(query, [address]);
    return result.rows[0] || null;
  });
}


/**
 * Get token holder balances from database with last fetch time metadata
 * @param tokenAddress The canister ID of the token
 * @returns Object containing holder balances map and metadata, or null if not found
 */
export const getTokenHolderBalancesFromDB = async (tokenAddress: string): Promise<{
  balances: Map<string, string>;
  lastFetchTime: Date | null;
  lastFetchIndex: string | null;
} | null> => {
  try {
    // First check if this token exists and get its metadata
    const client = await pool.connect();
    const tokenQuery = 'SELECT last_fetch_time, last_fetch_index FROM tokens WHERE address = $1';
    const tokenResult = await client.query(tokenQuery, [tokenAddress]);

    if (tokenResult.rows.length === 0) {
      console.log(`Token ${tokenAddress} not found in database`);
      return null;
    }

    const lastFetchTime = tokenResult.rows[0].last_fetch_time ? new Date(tokenResult.rows[0].last_fetch_time) : null;
    const lastFetchIndex = tokenResult.rows[0].last_fetch_index;

    // Get all balances for the token
    const balancesQuery = `
          SELECT a.account_id, tb.balance 
          FROM token_balances tb
          JOIN accounts a ON tb.account_id = a.id
          WHERE tb.token_address = $1
          ORDER BY tb.balance DESC
      `;
    const balancesResult = await client.query(balancesQuery, [tokenAddress]);

    // Convert to Map
    const balancesMap = new Map<string, string>();
    for (const row of balancesResult.rows) {
      balancesMap.set(row.account_id, row.balance.toString());
    }

    return {
      balances: balancesMap,
      lastFetchTime,
      lastFetchIndex
    };
  } catch (err) {
    console.error('Error getting token holder balances from DB:', err);
    return null;
  }
}

/**
* Save token holder balances to database
* @param tokenAddress The canister ID of the token
* @param balances Map of account IDs to balances
* @param lastFetchIndex The last fetch index from the canister
* @returns True if successful, false otherwise
*/
export const saveTokenHolderBalancesToDB = async (
  tokenAddress: string,
  balances: Map<string, bigint>,
  lastFetchIndex?: string
): Promise<boolean> => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');

    // Update token's fetch metadata
    const updateTokenQuery = `
          UPDATE tokens 
          SET last_fetch_time = CURRENT_TIMESTAMP, last_fetch_index = $2
          WHERE address = $1
      `;
    await client.query(updateTokenQuery, [tokenAddress, lastFetchIndex || null]);

    // Process all holders in batches
    const holderEntries = Array.from(balances.entries());
    const batchSize = 100; // Process 100 holders at a time

    for (let i = 0; i < holderEntries.length; i += batchSize) {
      const batch = holderEntries.slice(i, i + batchSize);

      // For each holder in the batch
      for (const [accountId, balance] of batch) {
        // Ensure account exists
        const accountQuery = `
                  INSERT INTO accounts(account_id) 
                  VALUES($1) 
                  ON CONFLICT(account_id) DO NOTHING 
                  RETURNING id
              `;
        const accountResult = await client.query(accountQuery, [accountId]);

        let dbAccountId;
        if (accountResult.rows.length > 0) {
          dbAccountId = accountResult.rows[0].id;
        } else {
          // Get existing account ID
          const existingAccountQuery = 'SELECT id FROM accounts WHERE account_id = $1';
          const existingAccount = await client.query(existingAccountQuery, [accountId]);
          dbAccountId = existingAccount.rows[0].id;
        }

        // Upsert the balance
        const balanceQuery = `
                  INSERT INTO token_balances(token_address, account_id, balance)
                  VALUES($1, $2, $3)
                  ON CONFLICT(token_address, account_id) 
                  DO UPDATE SET balance = $3, last_updated = CURRENT_TIMESTAMP
              `;
        await client.query(balanceQuery, [tokenAddress, dbAccountId, balance.toString()]);
      }
    }

    await client.query('COMMIT');
    return true;
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('Error saving token holder balances to DB:', err);
    return false;
  } finally {
    client.release();
  }
}

/**
* Check if a token's data is fresh based on its last fetch time
* @param tokenAddress The canister ID of the token
* @param maxAgeMinutes Maximum age of data in minutes before considering stale
* @returns Object with freshness status and metadata, or null if token not found
*/
export const checkTokenDataFreshness = async (tokenAddress: string, maxAgeMinutes: number = 1): Promise<{
  isFresh: boolean;
  lastFetchTime: Date | null;
  lastFetchIndex: string | null;
} | null> => {
  try {
    const client = await pool.connect();
    const query = 'SELECT last_fetch_time, last_fetch_index FROM tokens WHERE address = $1';
    const result = await client.query(query, [tokenAddress]);

    if (result.rows.length === 0) {
      return null; // Token not found
    }

    const lastFetchTime = result.rows[0].last_fetch_time ? new Date(result.rows[0].last_fetch_time) : null;
    const lastFetchIndex = result.rows[0].last_fetch_index;

    // Check if data is fresh enough
    const isFresh = lastFetchTime ?
      lastFetchTime > new Date(Date.now() - maxAgeMinutes * 60 * 1000) : false;

    return {
      isFresh,
      lastFetchTime,
      lastFetchIndex
    };
  } catch (err) {
    console.error('Error checking token data freshness:', err);
    return null;
  }
}

/**
* Gets the top token holders for a specific token
* @param tokenAddress The canister ID of the token
* @param limit Maximum number of holders to return
* @returns Array of holders with their balances
*/
export const getTopTokenHolders = async (tokenAddress: string, limit: number = 10): Promise<{ accountId: string, balance: string }[]> => {
  try {
    const client = await pool.connect();
    const query = `
          SELECT a.account_id, tb.balance 
          FROM token_balances tb
          JOIN accounts a ON tb.account_id = a.id
          WHERE tb.token_address = $1
          ORDER BY tb.balance DESC
          LIMIT $2
      `;
    const result = await client.query(query, [tokenAddress, limit]);

    return result.rows.map(row => ({
      accountId: row.account_id,
      balance: row.balance.toString()
    }));
  } catch (err) {
    console.error('Error getting top token holders:', err);
    return [];
  }
}

export const getBobTokens = async (): Promise<string[]> => {
  return withRetry(async (pool) => {
    //TODO get only active
    const query = `
      SELECT
        address
      FROM
        tokens
      WHERE
        standard = 'BOB-ICRC-2'
    `;
    const result = await pool.query(query);
    return result.rows.map(row => row.address);
  });
}
export const delBobTokens = async (addressToDel: string): Promise<string[]> => {
  return withRetry(async (pool) => {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // First delete token_metrics to avoid foreign key constraint violation
      await client.query('DELETE FROM token_metrics WHERE address = $1', [addressToDel]);

      // Then delete the token
      const query = `
        DELETE FROM tokens
        WHERE
          address = $1
        RETURNING
          address
      `;
      const result = await client.query(query, [addressToDel]);

      await client.query('COMMIT');
      return result.rows.map(row => row.address);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  });
}


export const getCurrentRobertChoice = async (): Promise<RobertChoiceMessageData | null> => {
  return withRetry(async (pool) => {
    const query = `
      SELECT address, ticker, name, description, created_at_token, created_by,
             zero_point_one_icp_tokens_out, image
      FROM robert_choice
      WHERE is_current = TRUE
      ORDER BY detected_at DESC
      LIMIT 1
    `;
    const result = await pool.query(query);
    return result.rows.length > 0 ? result.rows[0] : null;
  });
};

/**
 * Checks whether the provided Robert's choice data represents a new choice
 * by comparing it with the current choice stored in the database.
 *
 * @param choiceData - The Robert's choice data to check
 * @returns Promise<boolean> - True if this represents a new choice, false if it's the same as current
 * @throws Error if database operation fails
 */
export const isNewRobertChoice = async (choiceData: RobertChoiceMessageData): Promise<boolean> => {
  return withRetry(async (pool) => {
    const client = await pool.connect();
    try {
      // Check if this is already the current choice
      const currentChoice = await client.query(
        'SELECT address FROM robert_choice WHERE is_current = TRUE LIMIT 1'
      );

      // It's a new choice if there's no current choice or the address is different
      const isNew = currentChoice.rows.length === 0 ||
        currentChoice.rows[0].address !== choiceData.address;

      return isNew;
    } catch (error) {
      console.error('Error checking if Robert choice is new:', error);
      throw error;
    } finally {
      client.release();
    }
  });
};

/**
 * Updates the Robert's choice in the database. If the choice is new, it marks
 * all previous choices as not current and inserts the new choice. If it's the
 * same choice, it updates the timestamp.
 *
 * @param choiceData - The Robert's choice data to update
 * @param tokenName - The name of the token for the choice
 * @returns Promise<boolean> - True if this was a new choice, false if it was an update
 * @throws Error if database operation fails
 */
export const updateRobertChoice = async (choiceData: RobertChoiceMessageData, tokenName: string): Promise<boolean> => {
  return withRetry(async (pool) => {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Check if this is a new choice using the extracted function
      const isNewChoice = await isNewRobertChoice(choiceData);

      if (isNewChoice) {
        // Mark all previous choices as not current
        await client.query('UPDATE robert_choice SET is_current = FALSE');

        // Insert new choice
        await client.query(`
          INSERT INTO robert_choice (
            address, ticker, name, description, created_at_token, created_by,
            zero_point_one_icp_tokens_out, image, is_current
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, TRUE)
        `, [
          choiceData.address,
          choiceData.ticker,
          tokenName,
          choiceData.description,
          choiceData.created_at_token,
          choiceData.created_by,
          choiceData.zero_point_one_icp_tokens_out?.toString() || null,
          choiceData.image
        ]);

        console.log(`New Robert's choice detected: ${tokenName} (${choiceData.ticker})`);
      } else {
        // Update the existing current choice timestamp
        await client.query(
          'UPDATE robert_choice SET updated_at = CURRENT_TIMESTAMP WHERE is_current = TRUE'
        );
      }

      await client.query('COMMIT');
      return isNewChoice;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  });
};
export async function updateGroupConfigsAfterBonding(oldAddress: string, newAddress: string, pools: string[]): Promise<number> {
  return withRetry(async (pool) => {
    const query = `
      UPDATE group_configs 
      SET address = $1, updated_at = CURRENT_TIMESTAMP, pools = $3::varchar[]
      WHERE address = $2
      RETURNING id, group_id
    `;

    const { rows } = await pool.query(query, [newAddress, oldAddress, pools]);

    console.log(`Updated ${rows.length} group_configs from address ${oldAddress} to ${newAddress}`);
    if (rows.length > 0) {
      console.log('Updated group_configs:', rows.map(row => `ID: ${row.id}, Group ID: ${row.group_id}`));
    }

    return rows.length;
  });
}
