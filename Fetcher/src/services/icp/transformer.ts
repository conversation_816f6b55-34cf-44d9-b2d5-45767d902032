
import { BuyMessageData, BuyTokenData, NewPoolMessageData } from '../../types';
import { getTokenPrice, getTokenInfo } from '../database/db';

const ICP_ADDRESS = 'ryjl3-tyaaa-aaaaa-aaaba-cai'

function formatUTCDateTime(date: Date): string {
    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const day = String(date.getUTCDate()).padStart(2, '0');
    const hours = String(date.getUTCHours()).padStart(2, '0');
    const minutes = String(date.getUTCMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
}
function getHolderIncrease(holderToken1Balance: number, token1Change: number) {
    let holderIncrease = '';
    const previousBalance = holderToken1Balance - token1Change;
    // console.log('Debug holder increase:', {
    //     holderToken1Balance,
    //     token1Change,
    //     previousBalance,
    //     holderIncrease,
    //     calculation: `(${token1Change} / ${previousBalance}) * 100`
    // });
    if (previousBalance >= 0 && holderToken1Balance === token1Change) {
        holderIncrease = 'New Holder!';
    } else if (previousBalance > 0) {
        const percentageIncrease = (token1Change / previousBalance) * 100;
        // console.log('Debug holder increase:', { percentageIncrease });
        // Only show percentage if it's above 1%
        if (percentageIncrease > 1) {
            holderIncrease = `+${percentageIncrease.toFixed(2)}%`;
        }
    }
    return holderIncrease;
}

export function transformBuyTx(tx: any, holderToken1Balance: number, token1TotalSupply: number): BuyMessageData | null {
    const token0Change = Number(tx.token0ChangeAmount);
    const token1Change = Number(tx.token1ChangeAmount);
    const adjustedToken1TotalSupply = token1TotalSupply / 10 ** tx.token1Decimals
    // token0 is always spent, token1 is always received
    const spentToken: BuyTokenData = {
        address: tx.token0Id,
        symbol: tx.token0Symbol,
        name: tx.token0Symbol,
        amount: token0Change,
        priceUSD: tx.token0Price,
        pricePairToken: Math.abs(token1Change / token0Change)
    };

    const gotToken: BuyTokenData = {
        address: tx.token1Id,
        symbol: tx.token1Symbol,
        name: tx.token1Symbol,
        amount: token1Change,
        priceUSD: tx.token1Price,
        pricePairToken: Math.abs(token0Change / token1Change)
    };

    const holderIncrease = getHolderIncrease(holderToken1Balance, token1Change);
    const date = new Date(Number(tx.timestamp) * 1000);

    console.log(`token11TotalSupply: ${token1TotalSupply} {tx.token1Decimals: ${tx.token1Decimals}}, token1Price: ${tx.token1Price}`);
    const marketcap = adjustedToken1TotalSupply * tx.token1Price;
    const message: BuyMessageData = {
        spentToken,
        gotToken,
        pairAddress: tx.poolId,
        spentDollars: tx.amountUSD || 0,
        holderIncrease,
        holderWallet: tx.from,
        marketcap: marketcap,
        dex: 'ICP Swap',
        type: 'NewBuy',
        timestamp: formatUTCDateTime(date),
        holderPercentageOwned: (holderToken1Balance / adjustedToken1TotalSupply) * 100
    };

    return message;
}

export const trnasformNewPoolTx = (tx: any) => {
    const token1Change = tx.token1ChangeAmount;
    const token0Change = tx.token0ChangeAmount;
    const token0: BuyTokenData = {
        address: tx.token0Id,
        symbol: tx.token0Symbol,
        name: tx.token0Symbol,
        amount: tx.token0ChangeAmount,
        priceUSD: tx.token0Price,
        pricePairToken: Math.abs(token1Change / token0Change)
    };

    const token1: BuyTokenData = {
        address: tx.token1Id,
        symbol: tx.token1Symbol,
        name: tx.token1Symbol,
        amount: token1Change,
        priceUSD: tx.token1Price,
        pricePairToken: Math.abs(token0Change / token1Change)
    };
    if (tx.recipent === 'h7uwa-hyaaa-aaaam-qbgvq-cai') {
        var dex = 'ICPSwap-BOB'
    } else {
        var dex = 'ICPSwap'
    }
    const NewPoolMessageData: NewPoolMessageData = {
        canisterId: tx.poolId,
        token0: token0,
        token1: token1,
        dex: dex,
        type: 'NewPool'
    };
    return NewPoolMessageData
}


export const transformKongPools = (pools: any[]) => {
    const transformedPools = [];
    // const poolsMetrics = []; handle metrics here
    for (const pool of pools) {
        const transformedPool = {
            key: `KONG_${pool.symbol}`,
            fee: pool.lp_fee_bps,
            tickSpacing: '0',
            canisterId: `KONG_${pool.symbol}`,
            token0: { address: pool.address_0, standard: '' },
            token1: { address: pool.address_1, standard: '' },
        }
        transformedPools.push(transformedPool);
    }
    return transformedPools;
}

// Swap:{
//     ts:"1737022266972434608"
//     txs:[...
//     ]
//     request_id:346327
//     status:"Success"
//     tx_id:318757
//     transfer_ids:[...
//     ]
//     receive_chain:"IC"
//     mid_price:0.0003674666
//     pay_amount:865308551136
//     receive_amount:317971959
//     claim_ids:{}
//     pay_symbol:"TRAX"
//     receive_symbol:"ICP"
//     receive_address:"ryjl3-tyaaa-aaaaa-aaaba-cai"
//     pay_address:"emww2-4yaaa-aaaaq-aacbq-cai"
//     price:0.0003694164
//     pay_chain:"IC"
//     slippage:0.53
//     }
// txs:
//   {
//     ts: 1751784034726872058n,
//     receive_chain: 'IC',
//     pay_amount: 30000000n,
//     receive_amount: 37802951156778n,
//     pay_symbol: 'ICP',
//     receive_symbol: 'GFC',
//     receive_address: 'pwba7-ciaaa-aaaam-qcxia-cai',
//     pool_symbol: 'GFC_ICP',
//     pay_address: 'ryjl3-tyaaa-aaaaa-aaaba-cai',
//     price: 1256318,
//     pay_chain: 'IC',
//     lp_fee: 113408853470n,
//     gas_fee: 1000000n
//   }
export async function transformKongBuyTx(tx: any): Promise<BuyMessageData | null> {
    if (tx && tx.receive_address === 'ryjl3-tyaaa-aaaaa-aaaba-cai') {
        return null;
    }
    const token0Address = tx.pay_address;
    const token1Address = tx.receive_address;
    const token0Change = Number(tx.pay_amount);
    const token1Change = Number(tx.receive_amount);

    const token0Price = await getTokenPrice(token0Address);
    const token0Info = await getTokenInfo(token0Address);
    const token1Info = await getTokenInfo(token1Address);
    // console.log("transformKongBuyTx")
    const spentDollars = (token0Change / 10 ** (token0Info.decimals ? token0Info.decimals : 8)) * token0Price;
    const token1PriceUSD = spentDollars / (token1Change / 10 ** (token1Info.decimals ? token1Info.decimals : 8));
    const token1TotalSupply = token1Info.totalSupply;
    // token0 is always spent, token1 is always received
    const spentToken: BuyTokenData = {
        address: token0Address,
        symbol: tx.pay_symbol,
        name: token0Info.name,
        amount: token0Change / 10 ** (token0Info.decimals ? token0Info.decimals : 8),
        priceUSD: token0Price,
        pricePairToken: Math.abs(token1Change / token0Change)
    };

    const gotToken: BuyTokenData = {
        address: token1Address,
        symbol: tx.receive_symbol,
        name: token1Info.name,
        amount: token1Change / 10 ** (token1Info.decimals ? token1Info.decimals : 8),
        priceUSD: token1PriceUSD,
        pricePairToken: Math.abs(token0Change / token1Change)
    };
    console.log("tx.ts:", tx.ts)
    const date = new Date(Number(tx.ts / 1000000n));

    const poolAddress = `KONG_${tx.txs[0].pool_symbol}`;
    // can determine holder from kongSwap tx
    console.log(`token11TotalSupply: ${token1TotalSupply} {tx.token1Decimals: ${token1Info.decimals}}, token1Price: ${token1PriceUSD}`);
    const marketcap = (token1TotalSupply / 10 ** (token1Info.decimals ? token1Info.decimals : 8)) * token1PriceUSD;
    const message: BuyMessageData = {
        spentToken,
        gotToken,
        pairAddress: poolAddress,
        spentDollars: spentDollars,
        holderIncrease: undefined,
        holderWallet: undefined,
        marketcap: marketcap,
        dex: 'KongSwap',
        type: 'NewBuy',
        timestamp: formatUTCDateTime(date)
    };

    return message;
}




const bigIntToFloat = (value: BigInt, decimals: number): number => {
    return Number(value) / (10 ** decimals);
};

export const transformBobBuyTx = async (tokenData: any, tx: any, holderBalance: BigInt): Promise<BuyMessageData> => {
    // NOTE: token0 is the token being spent (always ICP in this case).
    // token1 is the token being received.

    const token0Info = await getTokenInfo(ICP_ADDRESS);
    const token0Price = await getTokenPrice(ICP_ADDRESS);
    const token0Change = tx.amount_e8s; // Amount of ICP spent, in smallest unit

    // Use the helper to convert BigInt amounts to floating-point numbers
    const spentAmount = bigIntToFloat(token0Change, token0Info.decimals);
    const reserveTokenAmount = bigIntToFloat((tokenData.liquidity_pool.reserve_token), 8);
    const totalSupply = bigIntToFloat((tokenData.liquidity_pool.total_supply), 8);
    const tokenAmountToCalc = totalSupply - reserveTokenAmount
    // Assuming 8 decimals for bob tokens
    const reserveIcpAmount = bigIntToFloat(tokenData.liquidity_pool.reserve_icp, token0Info.decimals);
    const spentDollars = spentAmount * Number(token0Price);

    const spentPricePairToken = tokenAmountToCalc / reserveIcpAmount;
    const icpCollected = reserveIcpAmount;
    const gotPricePairToken = (reserveIcpAmount / tokenAmountToCalc) * 2;
    const gotTokenPriceUSD = gotPricePairToken * Number(token0Price);
    const gotAmount = spentAmount * spentPricePairToken;

    const spentToken: BuyTokenData = {
        address: token0Info.address,
        symbol: token0Info.symbol,
        name: token0Info.name,
        amount: spentAmount,
        priceUSD: Number(token0Price),
        pricePairToken: spentPricePairToken
    };

    const gotToken: BuyTokenData = {
        address: String(tokenData.liquidity_pool.token_id),
        symbol: tokenData.token_info.ticker,
        name: tokenData.token_info.name,
        amount: gotAmount,
        priceUSD: gotTokenPriceUSD,
        pricePairToken: gotPricePairToken
    };
    const RealTotalSupplyFloat = 1000000000;

    let marketcap = gotTokenPriceUSD * RealTotalSupplyFloat;
    if (reserveIcpAmount < 1) {
        marketcap = 0;
    }
    const holderToken1Balance = bigIntToFloat(holderBalance, 8)
    const date = new Date(Number(tx.ts / 1000000n)); // Convert from nanoseconds to milliseconds
    const holderIncrease = getHolderIncrease(holderToken1Balance, gotAmount);
    return {
        spentToken: spentToken,
        gotToken: gotToken,
        spentDollars: spentDollars,
        pairAddress: 'bob.fun',
        holderWallet: tx.from.toString(),
        holderIncrease: holderIncrease,
        marketcap: marketcap,
        dex: 'bob.fun',
        type: 'NewBuy',
        icpCollected: icpCollected,
        timestamp: formatUTCDateTime(date),
        holderPercentageOwned: (holderToken1Balance / RealTotalSupplyFloat) * 100
    };
};
