import { IDL } from '@dfinity/candid';

export const dip20IdlFactory: IDL.InterfaceFactory = ({ IDL }) => {
  const TxReceipt = IDL.Variant({
    'Err': IDL.Variant({
      'AmountTooSmall': IDL.Null,
      'BlockUsed': IDL.Null,
      'ErrorOperationStyle': IDL.Null,
      'ErrorTo': IDL.Null,
      'InsufficientAllowance': IDL.Null,
      'InsufficientBalance': IDL.Null,
      'LedgerTrap': IDL.Null,
      'Other': IDL.Text,
      'Unauthorized': IDL.Null
    }),
    'Ok': IDL.Nat
  });
  const Time = IDL.Int;
  const Metadata = IDL.Record({
    'decimals': IDL.Nat8,
    'fee': IDL.Nat,
    'logo': IDL.Text,
    'name': IDL.Text,
    'owner': IDL.Principal,
    'symbol': IDL.Text,
    'totalSupply': IDL.Nat
  });
  const TokenInfo = IDL.Record({
    'cycles': IDL.Nat,
    'deployTime': Time,
    'feeTo': IDL.Principal,
    'historySize': IDL.Nat,
    'holderNumber': IDL.Nat,
    'metadata': Metadata
  });
  return IDL.Service({
    'allowance': IDL.Func([IDL.Principal, IDL.Principal], [IDL.Nat], ['query']),
    'approve': IDL.Func([IDL.Principal, IDL.Nat], [TxReceipt], []),
    'balanceOf': IDL.Func([IDL.Principal], [IDL.Nat], ['query']),
    'burn': IDL.Func([IDL.Nat], [TxReceipt], []),
    'decimals': IDL.Func([], [IDL.Nat8], ['query']),
    'getAllowanceSize': IDL.Func([], [IDL.Nat], ['query']),
    'getHolders': IDL.Func(
      [IDL.Nat, IDL.Nat],
      [IDL.Vec(IDL.Tuple(IDL.Principal, IDL.Nat))],
      ['query']
    ),
    'getMetadata': IDL.Func([], [Metadata], ['query']),
    'getTokenFee': IDL.Func([], [IDL.Nat], ['query']),
    'getTokenInfo': IDL.Func([], [TokenInfo], ['query']),
    'getUserApprovals': IDL.Func(
      [IDL.Principal],
      [IDL.Vec(IDL.Tuple(IDL.Principal, IDL.Nat))],
      ['query']
    ),
    'historySize': IDL.Func([], [IDL.Nat], ['query']),
    'logo': IDL.Func([], [IDL.Text], ['query']),
    'mint': IDL.Func([IDL.Principal, IDL.Nat], [TxReceipt], []),
    'name': IDL.Func([], [IDL.Text], ['query']),
    'setFee': IDL.Func([IDL.Nat], [], ['oneway']),
    'setFeeTo': IDL.Func([IDL.Principal], [], ['oneway']),
    'setLogo': IDL.Func([IDL.Text], [], ['oneway']),
    'setName': IDL.Func([IDL.Text], [], ['oneway']),
    'setOwner': IDL.Func([IDL.Principal], [], ['oneway']),
    'symbol': IDL.Func([], [IDL.Text], ['query']),
    'totalSupply': IDL.Func([], [IDL.Nat], ['query']),
    'transfer': IDL.Func([IDL.Principal, IDL.Nat], [TxReceipt], []),
    'transferFrom': IDL.Func(
      [IDL.Principal, IDL.Principal, IDL.Nat],
      [TxReceipt],
      []
    )
  });
};
