import { IDL } from '@dfinity/candid';


/**
 * icrc2IdlFactory generates the IDL for the ICRC2 standard, including transaction history.
 * 
 * @param {object} context - The context object passed to an InterfaceFactory.
 * @param {typeof IDL} context.IDL - The IDL object from @dfinity/candid, extracted from context.
 * @returns {IDL.ServiceClass} - The IDL service definition for the ICRC2 standard.
 */
export const icrc2IdlFactory: IDL.InterfaceFactory = ({ IDL }) => {
  const Account = IDL.Record({
    'owner': IDL.Principal,
    'subaccount': IDL.Opt(IDL.Vec(IDL.Nat8))
  });

  const StandardRecord = IDL.Record({
    'url': IDL.Text,
    'name': IDL.Text
  });

  const MetadataValue = IDL.Variant({
    'Int': IDL.Int,
    'Nat': IDL.Nat,
    'Text': IDL.Text,
    'Blob': IDL.Vec(IDL.Nat8)
  });

  // Types for get_transactions
  const T_GetBlocksRequest = IDL.Record({ 'start': IDL.Nat, 'length': IDL.Nat });
  const T_Mint = IDL.Record({ 'to': Account, 'memo': IDL.Opt(IDL.Vec(IDL.Nat8)), 'created_at_time': IDL.Opt(IDL.Nat64), 'amount': IDL.Nat });
  const T_Burn = IDL.Record({ 'from': Account, 'memo': IDL.Opt(IDL.Vec(IDL.Nat8)), 'created_at_time': IDL.Opt(IDL.Nat64), 'amount': IDL.Nat, 'spender': IDL.Opt(Account) });
  const T_Transfer = IDL.Record({ 'to': Account, 'fee': IDL.Opt(IDL.Nat), 'from': Account, 'memo': IDL.Opt(IDL.Vec(IDL.Nat8)), 'created_at_time': IDL.Opt(IDL.Nat64), 'amount': IDL.Nat, 'spender': IDL.Opt(Account) });
  const T_Approve = IDL.Record({
    'spender': Account,
    'amount': IDL.Nat,
    'expected_allowance': IDL.Opt(IDL.Nat),
    'expires_at': IDL.Opt(IDL.Nat64),
    'memo': IDL.Opt(IDL.Vec(IDL.Nat8)),
    'created_at_time': IDL.Opt(IDL.Nat64)
  });
  const T_Transaction = IDL.Record({ 'burn': IDL.Opt(T_Burn), 'kind': IDL.Text, 'mint': IDL.Opt(T_Mint), 'approve': IDL.Opt(T_Approve), 'timestamp': IDL.Nat64, 'transfer': IDL.Opt(T_Transfer) });

  const T_ArchiveResponse_Inner = IDL.Record({ 'transactions': IDL.Vec(T_Transaction) }); // Renamed to avoid conflict if used directly as callback return

  const T_ArchivedRange = IDL.Func(
    [T_GetBlocksRequest],
    [T_ArchiveResponse_Inner], // This is the type for the callback function's return value
    ['query']
  );
  
  // Define T_ArchivedRange_Record separately as the structure holding the callback
  const T_ArchivedRange_Record = IDL.Record({
      'callback': T_ArchivedRange, // Reference the function type directly
      'start': IDL.Nat,
      'length': IDL.Nat,
  });

  const T_GetTransactionsResponse = IDL.Record({
      'first_index': IDL.Nat,
      'log_length': IDL.Nat,
      'transactions': IDL.Vec(T_Transaction),
      'archived_transactions': IDL.Vec(T_ArchivedRange_Record), // Use T_ArchivedRange_Record here
  });

  return IDL.Service({
    // ICRC1 methods
    'icrc1_name': IDL.Func([], [IDL.Text], ['query']),
    'icrc1_symbol': IDL.Func([], [IDL.Text], ['query']),
    'icrc1_decimals': IDL.Func([], [IDL.Nat8], ['query']),
    'icrc1_total_supply': IDL.Func([], [IDL.Nat], ['query']),
    'icrc1_metadata': IDL.Func([], [IDL.Vec(IDL.Tuple(IDL.Text, MetadataValue))], ['query']),
    'icrc1_balance_of': IDL.Func([Account], [IDL.Nat], ['query']),
    'icrc1_supported_standards': IDL.Func([], [IDL.Vec(StandardRecord)], ['query']),
    'icrc1_minting_account': IDL.Func([], [IDL.Opt(Account)], ['query']),
    // ICRC3 / Get Transactions method
    'get_transactions': IDL.Func(
        [T_GetBlocksRequest],
        [T_GetTransactionsResponse],
        ['query'],
    ),
  });
};

// --- Archive Canister IDL Factory ---
export const archiveCanisterIdlFactory: IDL.InterfaceFactory = ({ IDL }) => {
  const T_GetBlocksRequest = IDL.Record({ 'start': IDL.Nat, 'length': IDL.Nat });
  const T_Account = IDL.Record({ 'owner': IDL.Principal, 'subaccount': IDL.Opt(IDL.Vec(IDL.Nat8)) });
  const T_Mint = IDL.Record({ 'to': T_Account, 'memo': IDL.Opt(IDL.Vec(IDL.Nat8)), 'created_at_time': IDL.Opt(IDL.Nat64), 'amount': IDL.Nat });
  const T_Burn = IDL.Record({ 'from': T_Account, 'memo': IDL.Opt(IDL.Vec(IDL.Nat8)), 'created_at_time': IDL.Opt(IDL.Nat64), 'amount': IDL.Nat, 'spender': IDL.Opt(T_Account) });
  const T_Transfer = IDL.Record({ 'to': T_Account, 'fee': IDL.Opt(IDL.Nat), 'from': T_Account, 'memo': IDL.Opt(IDL.Vec(IDL.Nat8)), 'created_at_time': IDL.Opt(IDL.Nat64), 'amount': IDL.Nat, 'spender': IDL.Opt(T_Account) });
  const T_Approve = IDL.Record({
    'spender': T_Account, // Use T_Account defined in this factory's scope
    'amount': IDL.Nat,
    'expected_allowance': IDL.Opt(IDL.Nat),
    'expires_at': IDL.Opt(IDL.Nat64),
    'memo': IDL.Opt(IDL.Vec(IDL.Nat8)),
    'created_at_time': IDL.Opt(IDL.Nat64)
  });
  const T_Transaction = IDL.Record({ 'burn': IDL.Opt(T_Burn), 'kind': IDL.Text, 'mint': IDL.Opt(T_Mint), 'approve': IDL.Opt(T_Approve), 'timestamp': IDL.Nat64, 'transfer': IDL.Opt(T_Transfer) });

  const T_ArchiveResponse = IDL.Record({ 'transactions': IDL.Vec(T_Transaction) });

  return IDL.Service({ 'get_transactions': IDL.Func([T_GetBlocksRequest], [T_ArchiveResponse], ['query']) });
};
