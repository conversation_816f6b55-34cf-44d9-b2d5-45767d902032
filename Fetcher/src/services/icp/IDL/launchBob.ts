// Save this as token.did.ts
import { IDL } from '@dfinity/candid';
import type { Principal } from '@dfinity/principal';
type IDLType = typeof IDL;

export const tokenFactory: IDL.InterfaceFactory = ({ IDL }) => {
  // ... other definitions ...

  // CORRECTED: Define this as a tuple, not a record.
  // This is a common structure for key-value pairs in Candid.
  const TopHolderTuple = IDL.Tuple(IDL.Principal, IDL.Nat64);

  // ... other definitions ...
  const CreatePositionRecord = IDL.Record({ 'amount0_desired': IDL.Text, 'tick_lower': IDL.Int32, 'tick_upper': IDL.Int32, 'amount1_desired': IDL.Text, });
  const TaskVariant = IDL.Variant({ 'DepositICP': IDL.Null, 'MintPosition': IDL.Null, 'DepositToken': IDL.Null, 'CreatePosition': CreatePositionRecord, 'CreatePoolKong': IDL.Null, });
  const OrderTypeVariant = IDL.Variant({ 'Buy': IDL.Null, 'Sell': IDL.Null });
  const OrderRecord = IDL.Record({ 'ts': IDL.Nat64, 'from': IDL.Principal, 'amount_e8s': IDL.Nat64, 'order_type': OrderTypeVariant, });
  const CandleRecord = IDL.Record({ 'low': IDL.Float64, 'high': IDL.Float64, 'close': IDL.Float64, 'open': IDL.Float64, 'time': IDL.Nat64, });
  const LiquidityPoolRecord = IDL.Record({ 'token_id': IDL.Nat64, 'reserve_token': IDL.Nat64, 'reserve_icp': IDL.Nat64, 'total_supply': IDL.Nat64, });
  const TokenInfoRecord = IDL.Record({
    'maybe_website': IDL.Opt(IDL.Text),
    'tasks': IDL.Vec(TaskVariant),
    'maybe_ledger': IDL.Opt(IDL.Principal),
    'ticker': IDL.Text,
    'maybe_icpswap_pool': IDL.Opt(IDL.Principal),
    'name': IDL.Text,
    'maybe_open_chat': IDL.Opt(IDL.Text),
    'description': IDL.Text,
    'created_at': IDL.Nat64,
    'created_by': IDL.Principal,
    'maybe_kong_swap': IDL.Opt(IDL.Bool),
    'tasks_v2': IDL.Opt(IDL.Vec(TaskVariant)),
    'image': IDL.Text,
    'maybe_twitter': IDL.Opt(IDL.Text),
    'maybe_telegram': IDL.Opt(IDL.Text)
  });

  const TokenDataRecord = IDL.Record({
    'top_holders': IDL.Vec(TopHolderTuple), // Use the corrected tuple definition here
    'orders': IDL.Vec(OrderRecord),
    'candles': IDL.Vec(CandleRecord),
    'liquidity_pool': LiquidityPoolRecord,
    'token_info': TokenInfoRecord,
  });

  const TokenInfoLandingRecord = IDL.Record({
    'ticker': IDL.Text,
    'token_id': IDL.Nat64,
    'name': IDL.Text,
    'description': IDL.Text,
    'created_at': IDL.Nat64,
    'created_by': IDL.Principal,
    'zero_point_one_icp_tokens_out': IDL.Opt(IDL.Nat64),
    'image': IDL.Text,
  });

  return IDL.Service({
    'get_token_balance': IDL.Func([IDL.Nat64, IDL.Principal], [IDL.Nat64], ['query']),
    'get_token_data': IDL.Func([IDL.Nat64], [TokenDataRecord], ['query']),
    'get_robert_choice_v2': IDL.Func([], [TokenInfoLandingRecord], ['query']),
    'get_token_info': IDL.Func([IDL.Nat64], [TokenInfoRecord], ['query']),
  });
};

// --- TypeScript Interfaces ---

export interface Account {
  'owner': Principal,
  'subaccount': [] | [Uint8Array | number[]],
}

export type TopHolder = [Principal, bigint];
export interface CreatePosition { 'amount0_desired': string; 'tick_lower': number; 'tick_upper': number; 'amount1_desired': string; }
export type Task = { 'DepositICP': null } | { 'MintPosition': null } | { 'DepositToken': null } | { 'CreatePosition': CreatePosition } | { 'CreatePoolKong': null };
export type OrderType = { 'Buy': null } | { 'Sell': null };
export interface Order { 'ts': bigint; 'from': Principal; 'amount_e8s': bigint; 'order_type': OrderType; }
export interface Candle { 'low': number; 'high': number; 'close': number; 'open': number; 'time': bigint; }
export interface LiquidityPool { 'token_id': bigint; 'reserve_token': bigint; 'reserve_icp': bigint; 'total_supply': bigint; }
export interface TokenInfo { 'maybe_website': [] | [string]; 'tasks': Array<Task>; 'maybe_ledger': [] | [Principal]; 'ticker': string; 'maybe_icpswap_pool': [] | [Principal]; 'name': string; 'maybe_open_chat': [] | [string]; 'description': string; 'created_at': bigint; 'created_by': Principal; 'maybe_kong_swap': [] | [boolean]; 'tasks_v2': [] | [Array<Task>]; 'image': string; 'maybe_twitter': [] | [string]; 'maybe_telegram': [] | [string]; }

export interface TokenData {
  'top_holders': Array<TopHolder>;
  'orders': Array<Order>;
  'candles': Array<Candle>;
  'liquidity_pool': LiquidityPool;
  'token_info': TokenInfo;
}
export type LiquidityTask = { 'DepositICP': null } |
{ 'MintPosition': null } |
{ 'DepositToken': null } |
{
  'CreatePosition': {
    'amount0_desired': string,
    'tick_lower': number,
    'tick_upper': number,
    'amount1_desired': string,
  }
} |
{ 'CreatePoolKong': null };
export interface TokenInfo {
  'maybe_website': [] | [string],
  'tasks': Array<LiquidityTask>,
  'maybe_ledger': [] | [Principal],
  'ticker': string,
  'maybe_icpswap_pool': [] | [Principal],
  'name': string,
  'maybe_open_chat': [] | [string],
  'description': string,
  'created_at': bigint,
  'created_by': Principal,
  'maybe_kong_swap': [] | [boolean],
  'tasks_v2': [] | [Array<LiquidityTask>],
  'image': string,
  'maybe_twitter': [] | [string],
  'maybe_telegram': [] | [string],
}
export interface TokenInfoLanding {
  'ticker': string;
  'token_id': bigint;
  'name': string;
  'description': string;
  'created_at': bigint;
  'created_by': Principal;
  'zero_point_one_icp_tokens_out': [] | [bigint];
  'image': string;
}

export interface _SERVICE {
  'get_token_balance': (tokenId: bigint, principal: Principal) => Promise<bigint>;
  'get_token_data': (tokenId: bigint) => Promise<TokenData>;
  'get_robert_choice_v2': () => Promise<TokenInfoLanding>;
  'get_token_info': (tokenId: bigint) => Promise<TokenInfo>;
}
