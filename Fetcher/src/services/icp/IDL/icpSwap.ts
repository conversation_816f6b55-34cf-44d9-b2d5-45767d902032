import { IDL } from '@dfinity/candid';

export const icpSwapFactory: IDL.InterfaceFactory = ({ IDL }) => {
  const Token = IDL.Record({
    'address': IDL.Text,
    'standard': IDL.Text,
  });

  const PoolData = IDL.Record({
    'fee': IDL.Nat,
    'key': IDL.Text,
    'tickSpacing': IDL.Int,
    'token0': Token,
    'token1': Token,
    'canisterId': IDL.Principal,
  });

  const Error = IDL.Variant({
    'CommonError': IDL.Null,
    'InternalError': IDL.Text,
    'UnsupportedToken': IDL.Text,
    'InsufficientFunds': IDL.Null,
  });

  const Result_3 = IDL.Variant({
    'ok': IDL.Vec(PoolData),
    'err': Error,
  });

  return IDL.Service({
    'getPools': IDL.Func([], [Result_3], ['query']),
  });
};
