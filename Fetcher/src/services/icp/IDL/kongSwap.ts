import type { Principal } from '@dfinity/principal';
import type { ActorMethod } from '@dfinity/agent';
import type { IDL } from '@dfinity/candid';

export interface AddLiquidityArgs {
  'token_0' : string,
  'token_1' : string,
  'amount_0' : bigint,
  'amount_1' : bigint,
  'tx_id_0' : [] | [TxId],
  'tx_id_1' : [] | [TxId],
}
export interface AddLiquidityReply {
  'ts' : bigint,
  'request_id' : bigint,
  'status' : string,
  'tx_id' : bigint,
  'add_lp_token_amount' : bigint,
  'transfer_ids' : Array<TransferIdReply>,
  'amount_0' : bigint,
  'amount_1' : bigint,
  'claim_ids' : BigUint64Array | bigint[],
  'address_0' : string,
  'address_1' : string,
  'symbol_0' : string,
  'symbol_1' : string,
  'chain_0' : string,
  'chain_1' : string,
  'symbol' : string,
}
export interface AddPoolArgs {
  'token_0' : string,
  'token_1' : string,
  'amount_0' : bigint,
  'amount_1' : bigint,
  'tx_id_0' : [] | [TxId],
  'tx_id_1' : [] | [TxId],
  'lp_fee_bps' : [] | [number],
}
export interface AddPoolReply {
  'ts' : bigint,
  'request_id' : bigint,
  'status' : string,
  'tx_id' : bigint,
  'lp_token_symbol' : string,
  'add_lp_token_amount' : bigint,
  'transfer_ids' : Array<TransferIdReply>,
  'name' : string,
  'amount_0' : bigint,
  'amount_1' : bigint,
  'claim_ids' : BigUint64Array | bigint[],
  'address_0' : string,
  'address_1' : string,
  'symbol_0' : string,
  'symbol_1' : string,
  'pool_id' : number,
  'chain_0' : string,
  'chain_1' : string,
  'is_removed' : boolean,
  'symbol' : string,
  'lp_fee_bps' : number,
}
export interface ICTokenReply {
  'fee' : bigint,
  'decimals' : number,
  'token_id' : number,
  'chain' : string,
  'name' : string,
  'canister_id' : string,
  'icrc1' : boolean,
  'icrc2' : boolean,
  'icrc3' : boolean,
  'is_removed' : boolean,
  'symbol' : string,
}
export interface ICTransferReply {
  'is_send' : boolean,
  'block_index' : bigint,
  'chain' : string,
  'canister_id' : string,
  'amount' : bigint,
  'symbol' : string,
}
export interface Icrc10SupportedStandards { 'url' : string, 'name' : string }
export interface Icrc28TrustedOriginsResponse {
  'trusted_origins' : Array<string>,
}
export interface LPTokenReply {
  'fee' : bigint,
  'decimals' : number,
  'token_id' : number,
  'chain' : string,
  'name' : string,
  'address' : string,
  'pool_id_of' : number,
  'is_removed' : boolean,
  'total_supply' : bigint,
  'symbol' : string,
}
export interface PoolReply {
  'tvl' : bigint,
  'lp_token_symbol' : string,
  'name' : string,
  'lp_fee_0' : bigint,
  'lp_fee_1' : bigint,
  'balance_0' : bigint,
  'balance_1' : bigint,
  'rolling_24h_volume' : bigint,
  'rolling_24h_apy' : number,
  'address_0' : string,
  'address_1' : string,
  'rolling_24h_num_swaps' : bigint,
  'symbol_0' : string,
  'symbol_1' : string,
  'pool_id' : number,
  'price' : number,
  'chain_0' : string,
  'chain_1' : string,
  'is_removed' : boolean,
  'symbol' : string,
  'rolling_24h_lp_fee' : bigint,
  'lp_fee_bps' : number,
}
export interface PoolsReply {
  'total_24h_lp_fee' : bigint,
  'total_tvl' : bigint,
  'total_24h_volume' : bigint,
  'pools' : Array<PoolReply>,
  'total_24h_num_swaps' : bigint,
}
export type PoolsResult = { 'Ok' : PoolsReply } |
  { 'Err' : string };
export interface RemoveLiquidityArgs {
  'token_0' : string,
  'token_1' : string,
  'remove_lp_token_amount' : bigint,
}
export interface RemoveLiquidityReply {
  'ts' : bigint,
  'request_id' : bigint,
  'status' : string,
  'tx_id' : bigint,
  'transfer_ids' : Array<TransferIdReply>,
  'lp_fee_0' : bigint,
  'lp_fee_1' : bigint,
  'amount_0' : bigint,
  'amount_1' : bigint,
  'claim_ids' : BigUint64Array | bigint[],
  'address_0' : string,
  'address_1' : string,
  'symbol_0' : string,
  'symbol_1' : string,
  'chain_0' : string,
  'chain_1' : string,
  'remove_lp_token_amount' : bigint,
  'symbol' : string,
}
export interface SwapArgs {
  'receive_token' : string,
  'max_slippage' : [] | [number],
  'pay_amount' : bigint,
  'referred_by' : [] | [string],
  'receive_amount' : [] | [bigint],
  'receive_address' : [] | [string],
  'pay_token' : string,
  'pay_tx_id' : [] | [TxId],
}
export interface SwapReply {
  'ts' : bigint,
  'txs' : Array<SwapTxReply>,
  'request_id' : bigint,
  'status' : string,
  'tx_id' : bigint,
  'transfer_ids' : Array<TransferIdReply>,
  'receive_chain' : string,
  'mid_price' : number,
  'pay_amount' : bigint,
  'receive_amount' : bigint,
  'claim_ids' : BigUint64Array | bigint[],
  'pay_symbol' : string,
  'receive_symbol' : string,
  'receive_address' : string,
  'pay_address' : string,
  'price' : number,
  'pay_chain' : string,
  'slippage' : number,
}
export interface SwapTxReply {
  'ts' : bigint,
  'receive_chain' : string,
  'pay_amount' : bigint,
  'receive_amount' : bigint,
  'pay_symbol' : string,
  'receive_symbol' : string,
  'receive_address' : string,
  'pool_symbol' : string,
  'pay_address' : string,
  'price' : number,
  'pay_chain' : string,
  'lp_fee' : bigint,
  'gas_fee' : bigint,
}
export type TokenReply = { 'IC' : ICTokenReply } |
  { 'LP' : LPTokenReply };
export type TokensResult = { 'Ok' : Array<TokenReply> } |
  { 'Err' : string };
export interface TransferIdReply {
  'transfer_id' : bigint,
  'transfer' : TransferReply,
}
export type TransferReply = { 'IC' : ICTransferReply };
export type TxId = { 'TransactionId' : string } |
  { 'BlockIndex' : bigint };
export type TxsReply = { 'AddLiquidity' : AddLiquidityReply } |
  { 'Swap' : SwapReply } |
  { 'AddPool' : AddPoolReply } |
  { 'RemoveLiquidity' : RemoveLiquidityReply };
export type TxsResult = { 'Ok' : Array<TxsReply> } |
  { 'Err' : string };
export interface _SERVICE {
  'icrc10_supported_standards' : ActorMethod<
    [],
    Array<Icrc10SupportedStandards>
  >,
  'icrc1_name' : ActorMethod<[], string>,
  'icrc28_trusted_origins' : ActorMethod<[], Icrc28TrustedOriginsResponse>,
  'pools' : ActorMethod<[[] | [string]], PoolsResult>,
  'tokens' : ActorMethod<[[] | [string]], TokensResult>,
  'txs' : ActorMethod<
    [[] | [string], [] | [bigint], [] | [number], [] | [number]],
    TxsResult
  >,
}
export declare const idlFactory: IDL.InterfaceFactory;
export declare const init: (args: { IDL: typeof IDL }) => IDL.Type[];

export const kongSwapIdl: IDL.InterfaceFactory = ({ IDL }) => {
  const TxId = IDL.Variant({
    'TransactionId': IDL.Text,
    'BlockIndex': IDL.Nat,
  });

  const TransferReply = IDL.Variant({
    'IC': IDL.Record({
      'is_send': IDL.Bool,
      'block_index': IDL.Nat,
      'chain': IDL.Text,
      'canister_id': IDL.Text,
      'amount': IDL.Nat,
      'symbol': IDL.Text,
    }),
  });

  const TransferIdReply = IDL.Record({
    'transfer_id': IDL.Nat64,
    'transfer': TransferReply,
  });

  const AddLiquidityArgs = IDL.Record({
    'token_0': IDL.Text,
    'token_1': IDL.Text,
    'amount_0': IDL.Nat,
    'amount_1': IDL.Nat,
    'tx_id_0': IDL.Opt(TxId),
    'tx_id_1': IDL.Opt(TxId),
  });

  const AddLiquidityReply = IDL.Record({
    'ts': IDL.Nat64,
    'request_id': IDL.Nat64,
    'status': IDL.Text,
    'tx_id': IDL.Nat64,
    'add_lp_token_amount': IDL.Nat,
    'transfer_ids': IDL.Vec(TransferIdReply),
    'amount_0': IDL.Nat,
    'amount_1': IDL.Nat,
    'claim_ids': IDL.Vec(IDL.Nat64),
    'address_0': IDL.Text,
    'address_1': IDL.Text,
    'symbol_0': IDL.Text,
    'symbol_1': IDL.Text,
    'chain_0': IDL.Text,
    'chain_1': IDL.Text,
    'symbol': IDL.Text,
  });

  const AddPoolArgs = IDL.Record({
    'token_0': IDL.Text,
    'token_1': IDL.Text,
    'amount_0': IDL.Nat,
    'amount_1': IDL.Nat,
    'tx_id_0': IDL.Opt(TxId),
    'tx_id_1': IDL.Opt(TxId),
    'lp_fee_bps': IDL.Opt(IDL.Nat8),
  });

  const AddPoolReply = IDL.Record({
    'ts': IDL.Nat64,
    'request_id': IDL.Nat64,
    'status': IDL.Text,
    'tx_id': IDL.Nat64,
    'lp_token_symbol': IDL.Text,
    'add_lp_token_amount': IDL.Nat,
    'transfer_ids': IDL.Vec(TransferIdReply),
    'name': IDL.Text,
    'amount_0': IDL.Nat,
    'amount_1': IDL.Nat,
    'claim_ids': IDL.Vec(IDL.Nat64),
    'address_0': IDL.Text,
    'address_1': IDL.Text,
    'symbol_0': IDL.Text,
    'symbol_1': IDL.Text,
    'pool_id': IDL.Nat32,
    'chain_0': IDL.Text,
    'chain_1': IDL.Text,
    'is_removed': IDL.Bool,
    'symbol': IDL.Text,
    'lp_fee_bps': IDL.Nat8,
  });

  const ICTokenReply = IDL.Record({
    'fee': IDL.Nat,
    'decimals': IDL.Nat8,
    'token_id': IDL.Nat32,
    'chain': IDL.Text,
    'name': IDL.Text,
    'canister_id': IDL.Text,
    'icrc1': IDL.Bool,
    'icrc2': IDL.Bool,
    'icrc3': IDL.Bool,
    'is_removed': IDL.Bool,
    'symbol': IDL.Text,
  });

  const ICTransferReply = IDL.Record({
    'is_send': IDL.Bool,
    'block_index': IDL.Nat,
    'chain': IDL.Text,
    'canister_id': IDL.Text,
    'amount': IDL.Nat,
    'symbol': IDL.Text,
  });

  const Icrc10SupportedStandards = IDL.Record({
    'url': IDL.Text,
    'name': IDL.Text,
  });

  const Icrc28TrustedOriginsResponse = IDL.Record({
    'trusted_origins': IDL.Vec(IDL.Text),
  });

  const LPTokenReply = IDL.Record({
    'fee': IDL.Nat,
    'decimals': IDL.Nat8,
    'token_id': IDL.Nat32,
    'chain': IDL.Text,
    'name': IDL.Text,
    'address': IDL.Text,
    'pool_id_of': IDL.Nat32,
    'is_removed': IDL.Bool,
    'total_supply': IDL.Nat,
    'symbol': IDL.Text,
  });

  const PoolReply = IDL.Record({
    'lp_token_symbol': IDL.Text,
    'name': IDL.Text,
    'lp_fee_0': IDL.Nat,
    'lp_fee_1': IDL.Nat,
    'balance_0': IDL.Nat,
    'balance_1': IDL.Nat,
    'address_0': IDL.Text,
    'address_1': IDL.Text,
    'symbol_0': IDL.Text,
    'symbol_1': IDL.Text,
    'pool_id': IDL.Nat32,
    'price': IDL.Float64,
    'chain_0': IDL.Text,
    'chain_1': IDL.Text,
    'is_removed': IDL.Bool,
    'symbol': IDL.Text,
    'lp_fee_bps': IDL.Nat8,
  });

  const PoolsReply = IDL.Record({
    'total_24h_lp_fee': IDL.Nat,
    'total_tvl': IDL.Nat,
    'total_24h_volume': IDL.Nat,
    'pools': IDL.Vec(PoolReply),
    'total_24h_num_swaps': IDL.Nat,
  });

  const PoolsResult = IDL.Variant({
    'Ok': IDL.Vec(PoolReply),
    'Err': IDL.Text,
  });

  const RemoveLiquidityArgs = IDL.Record({
    'token_0': IDL.Text,
    'token_1': IDL.Text,
    'remove_lp_token_amount': IDL.Nat,
  });

  const RemoveLiquidityReply = IDL.Record({
    'ts': IDL.Nat64,
    'request_id': IDL.Nat64,
    'status': IDL.Text,
    'tx_id': IDL.Nat64,
    'transfer_ids': IDL.Vec(TransferIdReply),
    'lp_fee_0': IDL.Nat,
    'lp_fee_1': IDL.Nat,
    'amount_0': IDL.Nat,
    'amount_1': IDL.Nat,
    'claim_ids': IDL.Vec(IDL.Nat64),
    'address_0': IDL.Text,
    'address_1': IDL.Text,
    'symbol_0': IDL.Text,
    'symbol_1': IDL.Text,
    'chain_0': IDL.Text,
    'chain_1': IDL.Text,
    'remove_lp_token_amount': IDL.Nat,
    'symbol': IDL.Text,
  });

  const SwapArgs = IDL.Record({
    'receive_token': IDL.Text,
    'max_slippage': IDL.Opt(IDL.Float64),
    'pay_amount': IDL.Nat,
    'referred_by': IDL.Opt(IDL.Text),
    'receive_amount': IDL.Opt(IDL.Nat),
    'receive_address': IDL.Opt(IDL.Text),
    'pay_token': IDL.Text,
    'pay_tx_id': IDL.Opt(TxId),
  });

  const SwapReply = IDL.Record({
    'ts': IDL.Nat64,
    'txs': IDL.Vec(IDL.Record({
      'ts': IDL.Nat64,
      'receive_chain': IDL.Text,
      'pay_amount': IDL.Nat,
      'receive_amount': IDL.Nat,
      'pay_symbol': IDL.Text,
      'receive_symbol': IDL.Text,
      'receive_address': IDL.Text,
      'pool_symbol': IDL.Text,
      'pay_address': IDL.Text,
      'price': IDL.Float64,
      'pay_chain': IDL.Text,
      'lp_fee': IDL.Nat,
      'gas_fee': IDL.Nat,
    })),
    'request_id': IDL.Nat64,
    'status': IDL.Text,
    'tx_id': IDL.Nat64,
    'transfer_ids': IDL.Vec(TransferIdReply),
    'receive_chain': IDL.Text,
    'mid_price': IDL.Float64,
    'pay_amount': IDL.Nat,
    'receive_amount': IDL.Nat,
    'claim_ids': IDL.Vec(IDL.Nat64),
    'pay_symbol': IDL.Text,
    'receive_symbol': IDL.Text,
    'receive_address': IDL.Text,
    'pay_address': IDL.Text,
    'price': IDL.Float64,
    'pay_chain': IDL.Text,
    'slippage': IDL.Float64,
  });

  const TokenReply = IDL.Variant({
    'IC': ICTokenReply,
    'LP': LPTokenReply,
  });

  const TokensResult = IDL.Variant({
    'Ok': IDL.Vec(TokenReply),
    'Err': IDL.Text,
  });

  const TxsReply = IDL.Variant({
    'AddLiquidity': AddLiquidityReply,
    'Swap': SwapReply,
    'AddPool': AddPoolReply,
    'RemoveLiquidity': RemoveLiquidityReply,
  });

  const TxsResult = IDL.Variant({
    'Ok': IDL.Vec(TxsReply),
    'Err': IDL.Text,
  });

  return IDL.Service({
    'icrc10_supported_standards': IDL.Func([], [IDL.Vec(Icrc10SupportedStandards)], ['query']),
    'icrc1_name': IDL.Func([], [IDL.Text], ['query']),
    'icrc28_trusted_origins': IDL.Func([], [Icrc28TrustedOriginsResponse], ['query']),
    'pools': IDL.Func([IDL.Opt(IDL.Text)], [PoolsResult], ['query']),
    'tokens': IDL.Func([IDL.Opt(IDL.Text)], [TokensResult], ['query']),
    'txs': IDL.Func([IDL.Opt(IDL.Text), IDL.Opt(IDL.Nat64), IDL.Opt(IDL.Nat32), IDL.Opt(IDL.Nat16)], [TxsResult], ['query']),
  });
};