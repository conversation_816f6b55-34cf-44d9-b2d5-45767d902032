import { Actor, HttpAgent } from '@dfinity/agent';
import { Principal } from '@dfinity/principal';
import { icrc2IdlFactory } from './IDL/icrc2';
import { dip20IdlFactory } from './IDL/dip20';
import { icpSwapFactory } from './IDL/icpSwap';
import { baseStorageidlFactory, SwapRecordInfo } from './IDL/baseStorage';
import { poolInfoidlFactory, Transaction } from './IDL/poolInfo';
import { kongSwapIdl } from './IDL/kongSwap';
import { WebSocketService } from '../websocket';
import { TokenData, NewPoolMessageData, StorageResponse, DIP20TokenInfo, TokenMetrics, BobOrder, BobCandle, RobertChoiceMessageData } from '../../types'
import {
  syncICPSwapPoolsToDb,
  getICPSwapPoolsByToken,
  upsertToken,
  getTokenInfo,
  updateTokenMetricsDb,
  getBobTokens,
  updateGroupConfigsAfterBonding,
  delBobTokens,
  getTokenPrice,
  updateRobertChoice,
  isNewRobertChoice,
  getTokenMetricsById

} from '../database/db';
import { transformBuyTx, trnasformNewPoolTx, transformKongPools, transformKongBuyTx, transformBobBuyTx } from './transformer';
import { tokensStorageIdl } from './IDL/tokensStorage';
import { getMetadataValue, bobCalculateTokenMetrics } from './utils';
import { updateTrending } from '../trending';
import { BobFetcher } from './bobFetcher'



const CYCLES_ADDRESS = 'aanaa-xaaaa-aaaah-aaeiq-cai'
const ICP_ADDRESS = 'ryjl3-tyaaa-aaaaa-aaaba-cai'

type KongTransactionType = 'AddPool' | 'AddLiquidity' | 'RemoveLiquidity' | 'Swap';


export class ICPService {
  private agent: HttpAgent;
  private websocket: WebSocketService;
  private BobFetcher: BobFetcher;
  private transactionFetchInterval: NodeJS.Timeout | null = null;
  private bobTransactionFetchInterval: NodeJS.Timeout | null = null;
  private robertChoiceInterval: NodeJS.Timeout | null = null;
  private baseStorageCanisterId = 'g54jq-hiaaa-aaaag-qck5q-cai';
  private baseTokenStorageCanisterId = 'ggzvv-5qaaa-aaaag-qck7a-cai';
  private icpSwapPoolsCanisterId = '4mmnk-kiaaa-aaaag-qbllq-cai';
  private kongCanisterId = 'cbefx-hqaaa-aaaar-qakrq-cai';
  private freshStart = true;
  private readonly overlapCount = 30;
  private readonly poolIntervalMs = 20000;
  private readonly transactionIntervalMs = 5000;
  private readonly bobTransactionIntervalMs = 10000;
  private stopRequested = false;
  private lastProcessedIndex: number = 0;
  private processedTransactions = new Map<string, number>();
  private bobProcessedTransactions = new Map<string, number>();
  private readonly transactionTTL = 60 * 60 * 1000;
  private currentStorageId: string | null = null;
  private isSyncing: boolean = false;
  private lastProcessedKongTx: number = 0;

  constructor() {
    this.agent = new HttpAgent({
      host: "https://ic0.app",
    });
    this.websocket = WebSocketService.getInstance();
    this.agent.syncTime();
    this.BobFetcher = new BobFetcher();
    // Start cleanup interval
    setInterval(() => this.cleanupOldTransactions(), 60 * 60 * 1000); // Clean every 60 minutes
  }

  private cleanupOldTransactions(): void {
    const now = Date.now();
    for (const [hash, timestamp] of this.processedTransactions.entries()) {
      if (now - timestamp > this.transactionTTL) {
        this.processedTransactions.delete(hash);
      }
    }
  }

  private getTransactionHash(tx: Transaction): string {
    return `${tx.from}_${tx.to}_${tx.timestamp}_${tx.token0ChangeAmount}_${tx.token1ChangeAmount}`;
  }


  private async getActor(canisterId: string, factory: any) {
    const principal = Principal.fromText(canisterId);
    return Actor.createActor(factory, {
      agent: this.agent,
      canisterId: principal,
    });
  }

  async checkAndHandleNewPool(tx: any) {
    try {
      // TODO move part to transformer
      if ('addLiquidity' in tx.action) {
        const poolData = {
          fee: tx.poolFee,
          key: `${tx.token0Id}_${tx.token1Id}_${tx.poolFee}`,
          tickSpacing: '0',
          token0: {
            address: tx.token0Id,
            standard: tx.token0Standard
          },
          token1: {
            address: tx.token1Id,
            standard: tx.token1Standard
          },
          canisterId: tx.poolId
        }
        const newPool = (await syncICPSwapPoolsToDb([poolData]));
        if (newPool.length > 0) {
          console.log('New pool detected!')
          const newPoolMessageData = trnasformNewPoolTx(tx)
          this.websocket.broadcast(newPoolMessageData);
          return true
        }

      }
    }
    catch (error) {
      console.error('Error processing new pool:', error);
    }
  }

  async processTransactions(transactions: Array<any>): Promise<void> {
    var processedTransactionsCount = 0;
    for (const tx of transactions) {
      try {
        if (await this.checkAndHandleNewPool(tx)) {
          console.log('New pool detected skipping sending as buy')
          continue
        }
        // Check if transaction was already processed
        if (tx.token1Id === CYCLES_ADDRESS || tx.token1Id === ICP_ADDRESS) {
          //TODO handle here cycles buy in future
          continue
        }
        const txHash = this.getTransactionHash(tx);
        if (this.processedTransactions.has(txHash)) {
          // console.log('Skipping duplicate transaction:', txHash);
          continue;
        }
        // console.log('tx:', tx)
        const holder = tx.from;
        const holderToken1Balance = await this.getBalanceOf(tx.token1Id, holder, tx.token1Standard);

        const tokenInfo = await this.fetchTokenInfo(tx.token1Id)
        if (!tokenInfo) {
          console.log('Token info not found for token:', tx.token1Id);
          continue
        }
        const adjustedHolderBalance = holderToken1Balance / 10 ** tokenInfo.decimals
        const message = transformBuyTx(tx, adjustedHolderBalance, Number(tokenInfo.totalSupply));

        if (message) {
          this.websocket.broadcast(message);
          // Mark transaction as processed
          this.processedTransactions.set(txHash, Date.now());
          processedTransactionsCount++;
        }
      } catch (error) {
        console.error('Error processing transaction:', error, tx);
      }
    }
    console.log(`Processed ${processedTransactionsCount} transactions`);
  }
  async processKongTransactions(transactions: Array<any>): Promise<void> {
    for (const tx of transactions) {
      const transformedTx = await transformKongBuyTx(tx.Swap);
      // console.log(tx.Swap.txs, 'tx.Swap.txs')
      if (transformedTx) {
        // console.log('Broadcasting Kong buy message from tx:', tx);
        this.websocket.broadcast(transformedTx);
      }
    }
  }
  async fetchTransactions(): Promise<void> {
    try {
      const kongTxs = await this.fetchKongTransactions()
      // console.log('Kong transactions:', kongTxs)
      if (kongTxs.length > 0) {
        await this.processKongTransactions(kongTxs.reverse());
      }
      const baseStorageActor = await this.getActor(this.baseStorageCanisterId, baseStorageidlFactory);
      const storages = (await baseStorageActor.baseStorage()) as string[];
      const startIndex = 0;

      if (storages.length === 0) {
        console.log('No storage canisters available');
        return;
      }

      const lastStorageId = storages[0];

      // Check if storage canister changed
      if (this.currentStorageId !== lastStorageId) {
        console.log(`Storage canister changed from ${this.currentStorageId} to ${lastStorageId}`);
        this.currentStorageId = lastStorageId;
        this.lastProcessedIndex = 0;
        this.freshStart = true;
      }

      const storage = await this.getActor(lastStorageId, poolInfoidlFactory);

      // Get total transactions
      const response = await storage.getBaseRecord(0n, 1n, []) as { totalElements: bigint };
      const total = Number(response.totalElements);

      console.log(`Total transactions: ${total}, Last processed: ${this.lastProcessedIndex}`);

      if (this.freshStart) {
        // On fresh start, fetch overlap transactions to save their hashes
        const fetchCount = Math.min(total - startIndex, this.overlapCount);

        console.log(`Fresh start - Fetching ${fetchCount} overlap transactions from index ${startIndex}`);

        const { content: transactions } = (await storage.getBaseRecord(
          BigInt(startIndex),
          BigInt(fetchCount),
          []
        )) as StorageResponse;

        // Save transaction hashes without processing
        for (const tx of transactions) {
          const txHash = this.getTransactionHash(tx);
          this.processedTransactions.set(txHash, Date.now());
        }

        this.lastProcessedIndex = total;
        this.freshStart = false;
        console.log('Fresh start - Saved overlap transaction hashes, lastProcessedIndex:', total);
        return;
      }

      // Skip if no new transactions
      if (total <= this.lastProcessedIndex) {
        return;
      }

      // Get new transactions from last processed index with overlap
      const newTransactionsCount = total - this.lastProcessedIndex;
      const fetchCount = Math.min(Math.min(newTransactionsCount + this.overlapCount, total - startIndex), this.overlapCount);

      console.log(`Fetching ${fetchCount} transactions (including ${this.overlapCount} overlap) from index ${startIndex}`);

      const { content: transactions } = (await storage.getBaseRecord(
        BigInt(startIndex),
        BigInt(fetchCount),
        []
      )) as StorageResponse;

      if (transactions.length > 0) {
        await this.processTransactions(transactions.reverse());
        this.lastProcessedIndex = total;
      }

    } catch (error) {
      console.error('Error in transaction fetcher:', error);
    }
  }

  async fetchKongTransactions(): Promise<any[]> {
    try {
      const actor = await this.getActor(this.kongCanisterId, kongSwapIdl);
      const result: Transaction[] = [];
      if (this.lastProcessedKongTx === 0) {
        const response = await actor.txs([], [], [], []) as any;
        if ('Ok' in response && response.Ok) {
          const initialTx = response.Ok[0];
          const txType = Object.keys(initialTx).find(key => ['AddPool', 'AddLiquidity', 'RemoveLiquidity', 'Swap'].includes(key)) as KongTransactionType;
          if (txType && initialTx[txType]) {
            console.log(`Initial Kong transaction (${txType}):`, initialTx[txType].tx_id);
            this.lastProcessedKongTx = initialTx[txType].tx_id;
            return response.Ok;
          }
        }
      }
      const response = await actor.txs([], [], [], [30]) as any;
      if ('Ok' in response && response.Ok) {
        for (const tx of response.Ok) {
          const txType = Object.keys(tx).find(key => ['AddPool', 'AddLiquidity', 'RemoveLiquidity', 'Swap'].includes(key)) as KongTransactionType;
          if (txType && tx[txType] && tx[txType].tx_id > this.lastProcessedKongTx) {
            // console.log(`New Kong transaction (${txType}):`, tx, tx[txType].tx_id, this.lastProcessedKongTx);
            result.push(tx);
            this.lastProcessedKongTx = tx[txType].tx_id;
          }
        }
      }
      return result;
    } catch (error) {
      console.error('Error fetching Kong transactions:', error);
      return [];
    }
  }

  async getPoolAddresses(canisterId: string): Promise<string[]> {
    try {
      const pools = await getICPSwapPoolsByToken(canisterId);
      return pools.map(pool => pool.canisterId);
    } catch (error) {
      console.error(`Failed to get pool addresses for ${canisterId}:`, error);
      return [];
    }
  }

  private async fetchTokenInfo(canisterId: string): Promise<TokenData | undefined> {
    try {
      const poolsAddresses = await this.getPoolAddresses(canisterId);
      const existingToken = await getTokenInfo(canisterId);

      if (existingToken) {
        try {
          let totalSupply: string;
          if (existingToken.timestamp > Date.now() - 1 * 60 * 1000) {
            existingToken.poolAddresses = poolsAddresses;
            return existingToken;
          }
          if (existingToken.standard === 'DIP20') {
            const actor = await this.getActor(canisterId, dip20IdlFactory);
            const tokenInfo = await actor.getTokenInfo() as DIP20TokenInfo;
            totalSupply = tokenInfo.metadata.totalSupply.toString();
          } else {
            const actor = await this.getActor(canisterId, icrc2IdlFactory);
            const supply = await actor.icrc1_total_supply() as string;
            totalSupply = supply.toString();
          }
          existingToken.totalSupply = totalSupply;
          existingToken.poolAddresses = poolsAddresses;

          await upsertToken(existingToken);
          return existingToken;
        } catch (error) {
          console.error(`Failed to update token ${canisterId}:`, error);
          return existingToken;
        }
      }

      // Try DIP20 first
      try {
        const dip20Actor = await this.getActor(canisterId, dip20IdlFactory);
        const tokenInfo = await dip20Actor.getTokenInfo() as DIP20TokenInfo;

        const tokenData: TokenData = {
          address: canisterId,
          name: tokenInfo.metadata.name,
          symbol: tokenInfo.metadata.symbol,
          decimals: Number(tokenInfo.metadata.decimals),
          totalSupply: tokenInfo.metadata.totalSupply.toString(),
          standard: 'DIP20',
          owner: tokenInfo.metadata.owner.toText(),
          poolAddresses: poolsAddresses,
        };
        await upsertToken(tokenData);
        return tokenData;
      } catch (error) {
        console.log(`Token ${canisterId} is not DIP20, trying ICRC...`);

        const icrcActor = await this.getActor(canisterId, icrc2IdlFactory)

        try {
          const [metadata, totalSupply, mintingAccount] = await Promise.all([
            icrcActor.icrc1_metadata() as any,
            icrcActor.icrc1_total_supply() as any,
            // icrcActor.icrc1_supported_standards() as any,
            icrcActor.icrc1_minting_account() as any
          ]);

          // Extract values from metadata

          const name = getMetadataValue(metadata, 'icrc1:name', 'Unknown');
          const symbol = getMetadataValue(metadata, 'icrc1:symbol', 'Unknown');
          const decimals = getMetadataValue(metadata, 'icrc1:decimals', 0);
          const owner = mintingAccount && mintingAccount[0] && mintingAccount[0].owner
            ? mintingAccount[0].owner.toText()
            : 'Unknown';
          const tokenData = {
            address: canisterId,
            name,
            symbol,
            decimals,
            totalSupply: totalSupply.toString(),
            standard: 'ICRC',
            owner: owner,
            poolAddresses: poolsAddresses,
            // holders: holders
          };

          // Upsert the new token info
          await upsertToken(tokenData);
          return tokenData;
        } catch (error) {
          console.error(`Failed to fetch ICRC token info for ${canisterId}:`, error);
        }
      }
    }
    catch (error) {
      console.error(`Failed to fetch token info for ${canisterId}:`, error);
    }
  }


  async getTokenInfo(canisterId: string): Promise<TokenData | undefined> {
    try {
      return await this.fetchTokenInfo(canisterId);
    } catch (error) {
      console.error(`Error fetching token info for ${canisterId}:`, error);
      throw error;
    }
  }

  private serializeBigInt(obj: any): any {
    if (typeof obj === 'bigint') {
      return obj.toString();
    }
    if (Array.isArray(obj)) {
      return obj.map(item => this.serializeBigInt(item));
    }
    if (typeof obj === 'object' && obj !== null) {
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        result[key] = this.serializeBigInt(value);
      }
      return result;
    }
    return obj;
  }


  async syncICPSwapPools(): Promise<void> {
    try {
      console.log('Starting pool sync...');
      const syncICPSwapPoolsStart = Date.now();
      // Get all pools from ICP Swap
      console.log('Fetching pools from ICP Swap...');
      let pools = await this.getICPSwapPools();
      if (!pools) {
        console.log('No pools found from ICP Swap');
        pools = [];
      }
      console.log(`Found ${pools.length} pools from ICP Swap`);

      console.log('Syncing pools to database...');
      const newPools = await syncICPSwapPoolsToDb(pools);
      for (const pool of newPools) {
        try {
          console.log('Fetching tokens info for pool', pool);
          const token0 = await this.fetchTokenInfo(pool.token0_address);
          const token1 = await this.fetchTokenInfo(pool.token1_address);
          if (!token0 || !token1) {
            console.log('No tokens found for pool', pool);
            continue;
          }
          const NewPoolMessageData: NewPoolMessageData = {
            canisterId: pool.canister_id,
            token0: { address: token0.address, name: token0.name, symbol: token0.symbol },
            token1: { address: token1.address, name: token1.name, symbol: token1.symbol },
            dex: 'ICPSwap',
            type: 'NewPool'
          };
          this.websocket.broadcast(NewPoolMessageData);
        } catch (error) {
          console.error(`Error fetching tokens info for pool ${pool.canister_id}:`, error);
        }
      }
      await this.syncKongPools();
      console.log('Completed pool sync');
      const syncICPSwapPoolsEnd = Date.now();
      console.log(`syncICPSwapPools took ${syncICPSwapPoolsEnd - syncICPSwapPoolsStart}ms`);
    } catch (error) {
      console.error('Error in syncICPSwapPools:', error);
    }
  }
  async syncKongPools(): Promise<void> {
    try {
      let kongPools = await this.getKongPools();
      // console.log(kongPools, 'kongPools')
      if (!kongPools) {
        console.log('No pools found from Kong swap');
        kongPools = [];
      }
      const transformedKongPools = transformKongPools(kongPools);
      const newKongPools = await syncICPSwapPoolsToDb(transformedKongPools);
      console.log(`Found new pools in Kong Swap`, newKongPools);
      for (const pool of newKongPools) {
        try {
          console.log('Fetching tokens info for pool', pool);
          const token0 = await this.fetchTokenInfo(pool.token0_address);
          const token1 = await this.fetchTokenInfo(pool.token1_address);
          if (!token0 || !token1) {
            console.log('No tokens found for pool', pool);
            continue;
          }
          if (newKongPools.length < 10) {
            console.log('More than 10 pools found skipping sending to alerts channel.');
            continue;
          }
          const NewPoolMessageData: NewPoolMessageData = {
            canisterId: pool.canister_id,
            token0: { address: token0.address, name: token0.name, symbol: token0.symbol },
            token1: { address: token1.address, name: token1.name, symbol: token1.symbol },
            dex: 'KongSwap',
            type: 'NewPool'
          };
          this.websocket.broadcast(NewPoolMessageData);
        } catch (error) {
          console.error(`Error fetching tokens info for pool ${pool.canister_id}:`, error);
        }
      }
    } catch (error) {
      console.error('Error in syncKongPools:', error);
    }
  }
  async startSyncingICP(): Promise<void> {
    try {
      console.log('Starting ICP sync loop...');
      while (!this.stopRequested) {
        if (this.isSyncing) {
          console.log('Sync already in progress, skipping this cycle...');
          await new Promise(resolve => setTimeout(resolve, this.poolIntervalMs));
          continue;
        }

        this.isSyncing = true;
        console.log('Starting new ICP sync cycle...');
        const startTime = Date.now();

        try {
          await Promise.all([
            (async () => {
              await this.syncICPSwapPools();
            })(),
            (async () => {
              await this.updateTokenMetrics();
            })(),
          ]);
          await updateTrending();
          const endTime = Date.now();
          const duration = endTime - startTime;
          console.log(`Completed ICP sync cycle in ${duration}ms`);
        } catch (error) {
          console.error('Error during ICP sync cycle:', error);
        } finally {
          this.isSyncing = false;
        }

        if (!this.stopRequested) {
          console.log(`Waiting ${this.poolIntervalMs}ms before next sync...`);
          await new Promise(resolve => setTimeout(resolve, this.poolIntervalMs));
        }
      }
    } catch (error) {
      console.error('Error in startSyncingICP:', error);
    }
  }

  stopSyncingICP(): void {
    this.stopRequested = true;
  }

  async startTransactionFetcher(): Promise<void> {
    if (this.transactionFetchInterval) {
      clearInterval(this.transactionFetchInterval);
    }

    // Flag to track if a fetch is in progress
    let isFetching = false;

    const fetchWithLock = async () => {
      if (isFetching) {
        console.log('Previous fetch still in progress, skipping...');
        return;
      }

      try {
        isFetching = true;
        await this.fetchTransactions();
      } catch (error) {
        console.error('Error in transaction fetcher:', error);
      } finally {
        isFetching = false;
      }
    };

    // Initial fetch
    await fetchWithLock();

    // Set up recurring fetch with lock
    this.transactionFetchInterval = setInterval(fetchWithLock, this.transactionIntervalMs);
  }

  stopTransactionFetcher(): void {
    if (this.transactionFetchInterval) {
      clearInterval(this.transactionFetchInterval);
      this.transactionFetchInterval = null;
    }
  }

  // Robert's Choice monitoring methods
  async startRobertChoiceMonitor(): Promise<void> {
    console.log('🎯 ENTERING startRobertChoiceMonitor method');
    try {
      console.log('🎯 Starting Robert\'s choice monitoring...');

      // Check immediately on start
      console.log('🔍 Performing initial Robert\'s choice check...');
      await this.checkRobertChoice();

      // Set up periodic checking every 5 minutes
      console.log('⏰ Setting up periodic interval...');
      this.robertChoiceInterval = setInterval(async () => {
        try {
          console.log('⏰ Periodic Robert\'s choice check...');
          await this.checkRobertChoice();
        } catch (error) {
          console.error('❌ Error in periodic Robert\'s choice check:', error);
        }
      }, 5 * 60 * 1000); // 5 minutes

      console.log('✅ Robert\'s choice monitoring started successfully');
    } catch (error) {
      console.error('❌ Failed to start Robert\'s choice monitoring:', error);
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      throw error;
    }
  }
  async checkRobertChoice(): Promise<void> {
    try {
      console.log('🔍 Checking Robert\'s choice...');

      // Get current choice from Bob canister
      console.log('📡 Fetching Robert\'s choice from Bob canister...');
      const robertChoice = await this.BobFetcher.getRobertChoiceV2();
      console.log('📊 Received Robert\'s choice:', {
        token_id: robertChoice.token_id.toString(),
        ticker: robertChoice.ticker,
        name: robertChoice.name
      });

      // Create minimal choice data for early comparison
      const minimalChoiceData: RobertChoiceMessageData = {
        address: robertChoice.token_id.toString(),
        name: robertChoice.name,
        ticker: robertChoice.ticker,
        description: robertChoice.description,
        created_at_token: new Date(Number(robertChoice.created_at) / 1000000),
        created_by: robertChoice.created_by.toString(),
        zero_point_one_icp_tokens_out: robertChoice.zero_point_one_icp_tokens_out[0] || undefined,
        image: robertChoice.image,
        type: 'RobertChoice'
      };

      // Early exit optimization: Check if this is a new choice before expensive operations
      console.log('🔍 Checking if Robert\'s choice is new...');
      const isNew = await isNewRobertChoice(minimalChoiceData);

      if (!isNew) {
        console.log('✅ Robert\'s choice unchanged, skipping expensive data retrieval operations');
        return;
      }

      console.log('🎯 New Robert\'s choice detected, proceeding with data retrieval...');

      // Fetch token information and metrics
      let tokenInfo = undefined;
      let socialLinks = {
        telegram: undefined as string | undefined,
        twitter: undefined as string | undefined,
        website: undefined as string | undefined,
        openChat: undefined as string | undefined
      };

      try {
        console.log('📊 Fetching token info for Robert\'s choice...');
        const basicTokenInfo = await getTokenInfo(robertChoice.token_id.toString());
        console.log('✅ Basic token info fetched successfully');

        // Fetch social links from Bob canister
        try {
          console.log('🔗 Fetching social links from Bob canister...');
          const bobTokenInfo = await this.BobFetcher.getTokenInfo(robertChoice.token_id);
          console.log('✅ Bob token info fetched successfully:', {
            maybe_telegram: bobTokenInfo.maybe_telegram,
            maybe_twitter: bobTokenInfo.maybe_twitter,
            maybe_website: bobTokenInfo.maybe_website,
            maybe_open_chat: bobTokenInfo.maybe_open_chat
          });

          // Extract social links
          socialLinks = {
            telegram: bobTokenInfo.maybe_telegram.length > 0 ? bobTokenInfo.maybe_telegram[0] : undefined,
            twitter: bobTokenInfo.maybe_twitter.length > 0 ? bobTokenInfo.maybe_twitter[0] : undefined,
            website: bobTokenInfo.maybe_website.length > 0 ? bobTokenInfo.maybe_website[0] : undefined,
            openChat: bobTokenInfo.maybe_open_chat.length > 0 ? bobTokenInfo.maybe_open_chat[0] : undefined
          };
          console.log('🔗 Social links extracted:', socialLinks);
        } catch (socialError) {
          console.warn('⚠️ Failed to fetch social links from Bob canister:', socialError);
          console.log('📝 Continuing without social links...');
          // socialLinks remains as the default empty object
        }

        // Fetch token metrics
        console.log('📈 Fetching token metrics for Robert\'s choice...');
        const tokenMetrics = await getTokenMetricsById(robertChoice.token_id.toString());
        console.log('✅ Token metrics fetched successfully');

        // Merge basic info with metrics
        tokenInfo = {
          ...basicTokenInfo,
          price_usd: tokenMetrics?.price_usd,
          price_usd_change: tokenMetrics?.price_usd_change,
          volume_usd_1d: tokenMetrics?.volume_usd_1d,
          volume_usd_7d: tokenMetrics?.volume_usd_7d,
          total_volume_usd: tokenMetrics?.total_volume_usd,
          fees_usd: tokenMetrics?.fees_usd,
          tx_count: tokenMetrics?.tx_count
        };

        console.log('📊 Final tokenInfo:', {
          hasBasicInfo: !!basicTokenInfo,
          hasMetrics: !!tokenMetrics,
          price_usd: tokenInfo.price_usd,
          tx_count: tokenInfo.tx_count,
          volume_usd_1d: tokenInfo.volume_usd_1d
        });

      } catch (error) {
        console.warn('⚠️ Could not fetch token info/metrics for Robert\'s choice:', error);
      }
      let creatorBalance = undefined;
      try {
        console.log('🔍 Fetching creator balance for Robert\'s choice...');
        const creatorPrincipalText = robertChoice.created_by.toText();
        console.log('🔍 Creator principal text:', creatorPrincipalText);
        creatorBalance = (await this.BobFetcher.getTokenBalance(robertChoice.token_id, robertChoice.created_by)).toString();
        console.log('✅ Creator balance fetched successfully:', creatorBalance);
      } catch (error) {
        console.warn('⚠️ Failed to fetch creator balance:', error);
      }

      // Convert to our database format
      const choiceData: RobertChoiceMessageData = {
        address: robertChoice.token_id.toString(),
        name: robertChoice.name,
        ticker: robertChoice.ticker,
        description: robertChoice.description,
        created_at_token: new Date(Number(robertChoice.created_at) / 1000000), // Convert from nanoseconds
        socialLinks: socialLinks,
        created_by: robertChoice.created_by.toString(),
        creatorBalance: creatorBalance,
        zero_point_one_icp_tokens_out: robertChoice.zero_point_one_icp_tokens_out[0] || undefined,
        image: robertChoice.image,
        type: 'RobertChoice',
        tokenInfo: tokenInfo || undefined
      };

      console.log('💾 Updating database with choice data...');
      // Update database and check if it's a new choice
      const isNewChoice = await updateRobertChoice(choiceData, robertChoice.name);

      if (isNewChoice) {
        const tokenName = robertChoice.name; // Use name from Robert's choice data
        console.log(`🎯 NEW Robert's choice detected: ${tokenName} (${choiceData.ticker}) - Address: ${choiceData.address}`);

        // Send WebSocket notification for new choice
        const robertChoiceMessage = {
          ...choiceData,
          name: tokenName, // Add name for WebSocket message compatibility
          type: 'robert_choice'
        };

        console.log('📤 Broadcasting Robert choice message:', {
          address: robertChoiceMessage.address,
          name: robertChoiceMessage.name,
          hasSocialLinks: !!robertChoiceMessage.socialLinks,
          socialLinks: robertChoiceMessage.socialLinks,
          hasTokenInfo: !!robertChoiceMessage.tokenInfo,
          tokenInfoSample: robertChoiceMessage.tokenInfo ? {
            price_usd: robertChoiceMessage.tokenInfo.price_usd,
            tx_count: robertChoiceMessage.tokenInfo.tx_count
          } : null
        });

        this.websocket.broadcast(robertChoiceMessage);
        console.log(`📡 Robert's choice message broadcasted via WebSocket`);
      } else {
        const tokenName = robertChoice.name; // Use name from Robert's choice data
        console.log(`✅ Robert's choice unchanged: ${tokenName} (${choiceData.ticker}) - Address: ${choiceData.address}`);
      }

    } catch (error) {
      console.error('❌ Failed to check Robert\'s choice:', error);
      console.error('Error details:', error);
    }
  }

  stopRobertChoiceMonitor(): void {
    if (this.robertChoiceInterval) {
      clearInterval(this.robertChoiceInterval);
      this.robertChoiceInterval = null;
      console.log('Robert\'s choice monitoring stopped');
    }
  }

  async start(): Promise<void> {
    console.log('🚀 Starting ICP Service...');

    try {
      console.log('🔌 Initializing websocket...');
      await this.websocket.initialize();
      console.log('✅ Websocket initialized');

      // Start transaction fetcher
      console.log('📡 Starting transaction fetcher...');
      await this.startTransactionFetcher();
      console.log('✅ Transaction fetcher started');

      console.log('🤖 Starting Bob transaction fetcher...');
      await this.startBobTransactionFetcher();
      console.log('✅ Bob transaction fetcher started');
      console.log('🎯 About to start Robert\'s choice monitor...');
      try {
        await this.startRobertChoiceMonitor();
        console.log('🎯 Robert\'s choice monitor started successfully');
      } catch (error) {
        console.error('❌ Failed to start Robert\'s choice monitor:', error);
      }
      // Start pool fetcher
      console.log('🏊 Starting pool fetcher...');
      await this.startSyncingICP();
      console.log('✅ Pool fetcher started');


      console.log('🎉 ICP Service started successfully');
    } catch (error) {
      console.error('💥 Failed to start ICP Service:', error);
      throw error;
    }
  }

  stop(): void {
    console.log('Stopping ICP Service...');

    // Stop pool fetcher
    this.stopSyncingICP();

    // Stop transaction fetcher
    this.stopTransactionFetcher();

    this.websocket.close();
    console.log('ICP Service stopped successfully');
  }

  async getBalanceOf(tokenCanisterId: string, address: string, standard: string): Promise<number> {
    try {
      if (standard === 'DIP20') {
        const actor = await this.getActor(tokenCanisterId, dip20IdlFactory);
        const balance = await actor.balanceOf(Principal.fromText(address));
        return Number(balance);
      } else {
        // ICRC2 standard
        const actor = await this.getActor(tokenCanisterId, icrc2IdlFactory);
        const balance = await actor.icrc1_balance_of({ owner: Principal.fromText(address), subaccount: [] });
        return Number(balance);
      }
    } catch (error) {
      console.error(`Failed to get balance for address ${address} of token ${tokenCanisterId}:`, error);
      return -1;
    }
  }

  private async getICPSwapPools(): Promise<any[] | undefined> {
    try {
      const actor = await this.getActor(this.icpSwapPoolsCanisterId, icpSwapFactory);
      const result = await actor.getPools() as any;

      if ('ok' in result && result.ok) {
        return result.ok;
      }
      throw new Error('Failed to get pools: ' + (result.err || 'Unknown error'));
    } catch (error) {
      console.error('Failed to get ICP swap pools:', error);
    }
  }

  private async getKongPools(): Promise<any | undefined> {
    try {
      const actor = await this.getActor(this.kongCanisterId, kongSwapIdl);
      const result = await actor.pools([]) as any;

      // Defensive: log the raw result if unexpected
      if (!result || typeof result !== 'object') {
        console.error('Record type error: Invalid result from pools()', result);
        throw new Error('Record type error: Invalid result from pools()');
      }

      if ('Ok' in result && result.Ok) {
        return result.Ok;
      }

      // Optionally, check for specific error codes if present
      if (result.err === 653 || result.err === 654) {
        console.error(`Record type error: Encountered error code ${result.err}`);
        throw new Error(`Record type error: Encountered error code ${result.err}`);
      }

      throw new Error('Failed to get pools: ' + (result.err || 'Unknown error'));
    } catch (error) {
      console.error('Failed to get ICP swap pools:', error);
    }
  }

  public async updateTokenMetrics(): Promise<void> {
    try {
      const updateTokenMetricsStart = Date.now();
      // Retrieve the main actor for the base token storage canister
      const actor = await this.getActor(this.baseTokenStorageCanisterId, tokensStorageIdl);

      // Fetch all token storage IDs
      const tokenStorages = await actor.allTokenStorage() as string[];

      // Create an array of promises for processing each token storage
      const updatePromises = tokenStorages.map(async (tokenStorageId) => {
        try {
          // Retrieve the actor for the specific token storage
          const tokenStorageActor = await this.getActor(tokenStorageId, tokensStorageIdl);

          // Fetch all token information from this storage
          const tokensInfo = await tokenStorageActor.getAllTokens() as TokenMetrics[];
          await updateTokenMetricsDb(tokensInfo);
        } catch (innerError) {
          console.error(`Failed to process token storage: ${tokenStorageId}`, innerError);
        }
      });

      // Wait for all promises to complete
      await Promise.all(updatePromises);
      const updateTokenMetricsEnd = Date.now();
      console.log('All token metrics updated successfully');
      console.log(`updateTokenMetrics took ${updateTokenMetricsEnd - updateTokenMetricsStart}ms`);
    } catch (error) {
      console.error('Failed to update token metrics:', error);
    }
  }

  async updateTokenAfterBonding(oldAddress: string, newAddress: string): Promise<void> {
    console.log('Updating token after bonding', oldAddress, newAddress);
    const tokenInfo = await this.fetchTokenInfo(newAddress.toString())
    if (tokenInfo) {
      console.log('Found token info for new address', tokenInfo);
      console.log('Getting pool addresses for new address')
      let pools = await this.getPoolAddresses(newAddress.toString())
      if (pools.length === 0) {
        console.log('No pools found for token', newAddress.toString())
        console.log('Waiting 10 seconds and retrying...')
        await new Promise(resolve => setTimeout(resolve, 10000))
        pools = await this.getPoolAddresses(newAddress.toString())
      }
      await updateGroupConfigsAfterBonding(oldAddress, newAddress.toString(), pools)
    }
  }

  async processBobTransactions(token: string): Promise<void> {
    const tokenData = await this.BobFetcher.getTokenData(BigInt(token))
    const address = String(tokenData.liquidity_pool.token_id)

    const transactions = tokenData.orders;
    const tokenBobLastProcessedTransactionIdx = this.bobProcessedTransactions.get(token) || 0
    if (transactions.length > tokenBobLastProcessedTransactionIdx) {
      try {
        // Get ICP price for volume calculations
        const icpPrice = await getTokenPrice('ryjl3-tyaaa-aaaaa-aaaba-cai') || 10; // Default to $10 if not found
        console.log(`Using ICP price: $${icpPrice}`);

        const metrics = bobCalculateTokenMetrics(address, tokenData.liquidity_pool, tokenData.orders as BobOrder[], tokenData.candles, 0, icpPrice)
        console.log('metrics', metrics)
        await updateTokenMetricsDb([metrics])

      } catch (error) {
        console.error('Failed to calculate token metrics:', error);
      }
    }
    console.log(`current tokenBobLastProcessedTransactionIdx for ${token}: ${tokenBobLastProcessedTransactionIdx}`)
    const ledgerCanisterId = tokenData.token_info.maybe_ledger
    console.log(`ledgerCanisterId for ${token}: ${ledgerCanisterId}`)
    if (ledgerCanisterId && ledgerCanisterId.length > 0) {
      console.log(`ledgerCanisterId for ${token}: ${ledgerCanisterId}`)
      await this.updateTokenAfterBonding(address, ledgerCanisterId.toString())
      console.log(`Deleting bob token ${address} from tokens table`)
      await delBobTokens(address)
    }
    if (tokenBobLastProcessedTransactionIdx === 0) {
      console.log("Fresh start. setting processed index to transaction.length")
      this.bobProcessedTransactions.set(address, transactions.length)
      return
    }

    for (const tx of transactions.slice(tokenBobLastProcessedTransactionIdx, transactions.length)) {

      if ('Buy' in tx.order_type) {
        console.log("BUY TRANSACTION DETECTED")
        const holderBalance = await this.BobFetcher.getTokenBalance(BigInt(token), tx.from)
        console.log(holderBalance, 'holderBalance')
        const message = await transformBobBuyTx(tokenData, tx, holderBalance);

        if (message) {
          console.log('Broadcasting buy message from tx:', tx);
          this.websocket.broadcast(message);
          // Mark transaction as processed

        }
      }
    }
    this.bobProcessedTransactions.set(address, transactions.length)
  }

  async startBobTransactionFetcher(): Promise<void> {
    if (this.bobTransactionFetchInterval) {
      clearInterval(this.bobTransactionFetchInterval);
    }

    // Flag to track if a fetch is in progress
    let isFetching = false;

    const fetchWithLockBob = async () => {
      if (isFetching) {
        console.log('Previous fetch still in progress, skipping...');
        return;
      }

      try {
        isFetching = true;
        const bobTokens = await getBobTokens()
        for (const token of bobTokens) {
          this.processBobTransactions(token)
        }
      } catch (error) {
        console.error('Error in transaction fetcher:', error);
      } finally {
        isFetching = false;
      }
    };

    // Initial fetch
    await fetchWithLockBob();

    // Set up recurring fetch with lock
    this.bobTransactionFetchInterval = setInterval(fetchWithLockBob, this.bobTransactionIntervalMs);
  }
}