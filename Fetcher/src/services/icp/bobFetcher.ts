// This file remains correct.
import { Actor, ActorSubclass, HttpAgent, HttpAgentOptions } from '@dfinity/agent';
import { tokenFactory, _SERVICE, TokenData, TokenInfoLanding, TokenInfo } from './IDL/launchBob';
import { Principal } from '@dfinity/principal';
const CANISTER_ID = 'h7uwa-hyaaa-aaaam-qbgvq-cai';

export class BobFetcher {
  public static readonly canisterId: string = CANISTER_ID;
  private actor: ActorSubclass<_SERVICE>;

  constructor(agentOptions?: HttpAgentOptions) {
    const agent = new HttpAgent({
      host: 'https://icp-api.io',
      ...agentOptions,
    });

    if (process.env.DFX_NETWORK !== 'ic') {
      agent.fetchRootKey().catch(err => {
        console.warn("Unable to fetch root key.");
        console.error(err);
      });
    }

    // This call will now work correctly with the updated tokenFactory
    this.actor = Actor.createActor(tokenFactory, {
      agent,
      canisterId: CANISTER_ID,
    });
  }

  public async getTokenData(tokenId: bigint): Promise<TokenData> {
    return this.actor.get_token_data(tokenId);
  }
  public async getTokenBalance(tokenId: bigint, principal: Principal): Promise<BigInt> {
    return this.actor.get_token_balance(tokenId, principal);
  }
  public async getRobertChoiceV2(): Promise<TokenInfoLanding> {
    return this.actor.get_robert_choice_v2();
  }
  public async getTokenInfo(tokenId: bigint): Promise<TokenInfo> {
    try {
      console.log('🔍 Using get_token_data to fetch social links for token:', tokenId.toString());
      const tokenData = await this.actor.get_token_data(tokenId);
      console.log('✅ get_token_data result received');

      if (tokenData && tokenData.token_info) {
        console.log('✅ Extracted token_info from get_token_data');
        console.log('🔗 Social links available:', {
          maybe_telegram: tokenData.token_info.maybe_telegram,
          maybe_twitter: tokenData.token_info.maybe_twitter,
          maybe_website: tokenData.token_info.maybe_website,
          maybe_open_chat: tokenData.token_info.maybe_open_chat
        });
        return tokenData.token_info;
      } else {
        throw new Error('No token_info found in get_token_data response');
      }
    } catch (error) {
      console.error('❌ get_token_data error:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack'
      });
      throw error;
    }
  }
}
