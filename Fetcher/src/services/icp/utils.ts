import { HttpAgent } from "@dfinity/agent";
import { Actor } from "@dfinity/agent";
import { Principal } from "@dfinity/principal";
import { kongSwapIdl } from "./IDL/kongSwap";
import { <PERSON><PERSON><PERSON><PERSON>, BobCandle, TokenMetrics } from '../../types';
import { LiquidityPool } from './IDL/launchBob';

export const getMetadataValue = (metadata: any[], key: string, defaultValue: any) => {
  const item = metadata.find((m: any) => m[0] === key);
  if (!item) return defaultValue;
  const variant = item[1];
  return 'Text' in variant ? variant.Text :
    'Nat' in variant ? Number(variant.Nat) :
      defaultValue;
};

// export const getPriceKongSwap = async (symbol: string) => {
//     try {
//         const agent = new HttpAgent({
//             host: "https://ic0.app",
//           });
//       const principal = Principal.fromText('cbefx-hqaaa-aaaar-qakrq-cai');
//       const actor = Actor.createActor(kongSwapIdl, {
//         agent: agent,
//         canisterId: principal,
//       });
//       const result = await actor.pools([`${symbol}_ckUSDT`]) as any;
//       if (result.Ok) {
//           return result.Ok[0].price;
//       }
//     } catch (error) {
//         console.error(error);
//         return 0;
//     }
//   }




/**
 * Calculate token metrics from Bob orders and candles data
 * @param tokenAddress The token address/canister ID
 * @param orders Array of Bob orders
 * @param candles Array of Bob candles
 * @param feePercentage Fee percentage (default 0.3% = 0.003)
 * @returns TokenMetrics object with all calculated fields
 */
export function bobCalculateTokenMetrics(
  tokenAddress: string,
  liquidity_pool: LiquidityPool,
  orders: BobOrder[],
  candles: BobCandle[],
  feePercentage: number,
  icpPriceUSD: number // Default ICP price, should be passed from caller
): TokenMetrics {
  const now = Date.now();
  const oneDayAgo = now - (24 * 60 * 60 * 1000);
  const sevenDaysAgo = now - (7 * 24 * 60 * 60 * 1000);

  // Convert nanosecond timestamps to milliseconds for comparison
  const oneDayAgoNs = BigInt(oneDayAgo * 1000000);
  const sevenDaysAgoNs = BigInt(sevenDaysAgo * 1000000);

  // Calculate current price using the same method as transformBobBuyTx
  const bigIntToFloat = (value: bigint, decimals: number): number => {
    return Number(value) / (10 ** decimals);
  };

  // Use the exact same calculation as in transformer.ts
  const reserveTokenAmount = bigIntToFloat(liquidity_pool.reserve_token, 8);
  const totalSupply = bigIntToFloat(liquidity_pool.total_supply, 8);
  const tokenAmountToCalc = totalSupply - reserveTokenAmount;
  const reserveIcpAmount = bigIntToFloat(liquidity_pool.reserve_icp, 8); // ICP has 8 decimals

  // Calculate price in ICP per token (same as transformer.ts line 256)
  const gotPricePairToken = (reserveIcpAmount / tokenAmountToCalc) * 2;

  // Convert to USD using ICP price (same as transformer.ts line 257)
  const currentPrice = gotPricePairToken * icpPriceUSD;

  // Get price from 24h ago for price change calculation
  let oneDayAgoPrice = currentPrice; // Default to current price
  let priceChange = 0;



  if (candles.length > 1) {
    // Find candle closest to 24h ago
    const targetTime = oneDayAgo / 1000; // Convert to seconds
    const comparisonCandle = candles.reduce((closest, candle) => {
      const candleTimeDiff = Math.abs(Number(candle.time) - targetTime);
      const closestTimeDiff = Math.abs(Number(closest.time) - targetTime);
      return candleTimeDiff < closestTimeDiff ? candle : closest;
    });

    // Convert candle price to USD (candle prices are in ICP per token)
    oneDayAgoPrice = comparisonCandle.open * icpPriceUSD;

    // Calculate price change percentage
    if (oneDayAgoPrice > 0 && currentPrice > 0) {
      priceChange = ((currentPrice - oneDayAgoPrice) / oneDayAgoPrice) * 100;
    }
  }

  // Filter orders by time periods
  const orders1d = orders.filter(order => order.ts >= oneDayAgoNs);
  const orders7d = orders.filter(order => order.ts >= sevenDaysAgoNs);

  // Calculate volumes based on order type
  const calculateVolumeUSD = (orderList: BobOrder[], icpPriceUSD: number): number => {

    return orderList.reduce((total, order) => {
      const rawAmount = order.amount_e8s;
      const orderType = Object.keys(order.order_type)[0];

      let volumeUSD = 0;

      if (orderType === 'Buy') {
        // For buy orders, amount_e8s is ICP amount
        const icpAmount = Number(rawAmount) / 10 ** 8; // Convert from e8s to ICP
        volumeUSD = icpAmount * icpPriceUSD;
      } else if (orderType === 'Sell') {
        // For sell orders, amount_e8s is token amount, need to convert to ICP value
        const tokenAmount = Number(rawAmount) / 10 ** 8; // Convert from e8s to tokens
        const tokenPriceInICP = gotPricePairToken; // Price in ICP per token
        const icpAmount = tokenAmount * tokenPriceInICP; // Convert to ICP equivalent
        volumeUSD = icpAmount * icpPriceUSD;
      }

      // Cap individual orders at $10,000 to prevent historical outliers from skewing total volume
      volumeUSD = Math.min(volumeUSD, 10000);

      return total + volumeUSD;
    }, 0);
  };

  const volume1d = calculateVolumeUSD(orders1d, icpPriceUSD);
  const volume7d = calculateVolumeUSD(orders7d, icpPriceUSD);
  const totalVolume = calculateVolumeUSD(orders, icpPriceUSD);

  // Calculate fees (percentage of volume)
  const fees1d = volume1d * feePercentage;

  // Transaction count (total number of orders)
  const txCount = orders.length;

  return {
    address: tokenAddress,
    volumeUSD1d: volume1d,
    volumeUSD7d: volume7d,
    totalVolumeUSD: totalVolume,
    volumeUSD: volume7d, // Using 7d volume as current volume
    feesUSD: fees1d,
    priceUSDChange: priceChange,
    txCount: txCount,
    priceUSD: currentPrice
  };
}
