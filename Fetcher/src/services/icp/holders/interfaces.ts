// src/interfaces.ts
import type { Principal } from '@dfinity/principal';
import type { ActorMethod } from '@dfinity/agent';

// Basic Types
export interface Account {
  'owner': Principal;
  'subaccount': [] | [Uint8Array | number[]];
}

// Transaction Kinds
export interface Mint {
  'to': Account;
  'memo': [] | [Uint8Array | number[]];
  'created_at_time': [] | [bigint];
  'amount': bigint;
}

export interface Burn {
  'from': Account;
  'memo': [] | [Uint8Array | number[]];
  'created_at_time': [] | [bigint];
  'amount': bigint;
  'spender': [] | [Account];
}

export interface Transfer {
  'to': Account;
  'fee': [] | [bigint];
  'from': Account;
  'memo': [] | [Uint8Array | number[]];
  'created_at_time': [] | [bigint];
  'amount': bigint;
  'spender': [] | [Account];
}

export interface Approve {
  'spender': Account;
  'from': Account;
  'amount': bigint;
  'fee': [] | [bigint];
  'expected_allowance': [] | [bigint];
  'expires_at': [] | [bigint];
  'memo': [] | [Uint8Array | number[]];
  'created_at_time': [] | [bigint];
}

// Combined Transaction Structure
export interface Transaction {
  'burn': [] | [Burn];
  'kind': string; // "mint", "burn", "transfer", "approve"
  'mint': [] | [Mint];
  'approve': [] | [Approve]; // Updated to use Approve interface
  'timestamp': bigint;
  'transfer': [] | [Transfer];
}

// Request Structure for get_transactions
export interface GetBlocksRequest {
  'start': bigint;
  'length': bigint;
}

// Archive Response Structure (Simple)
export interface ArchiveResponse {
  'transactions': Transaction[];
}

// Archive Range Structure (from main canister response)
export interface ArchivedRange {
  'callback': ActorMethod<[GetBlocksRequest], ArchiveResponse>; // Adjusted callback type
  'start': bigint;
  'length': bigint;
}

// Full GetTransactions Response Structure (from main canister)
export interface GetTransactionsResponse {
  'first_index': bigint;
  'log_length': bigint;
  'transactions': Transaction[];
  'archived_transactions': ArchivedRange[];
}

// Service interface for the main canister actor
export interface MainICRC2Service {
  'get_transactions': ActorMethod<[GetBlocksRequest], GetTransactionsResponse>;
  // Add other methods if needed
}

// Interface for the archive canister actor (using the simpler response)
// Note: The method name ('get_transactions') is assumed based on common practice.
export interface ArchiveICRC2Service {
    'get_transactions': ActorMethod<[GetBlocksRequest], ArchiveResponse>;
}
