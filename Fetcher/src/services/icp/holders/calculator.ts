// src/token-calculator.ts
import { Actor, HttpAgent } from '@dfinity/agent';
import type { Principal } from '@dfinity/principal';
import { icrc2IdlFactory, archiveCanisterIdlFactory } from '../IDL/icrc2';
import type { Transaction, MainICRC2Service, ArchiveICRC2Service, ArchiveResponse, GetTransactionsResponse } from './interfaces'; // Use defined interfaces
import { checkTokenDataFreshness, getTokenHolderBalancesFromDB, saveTokenHolderBalancesToDB } from '../../database/db';

const MAX_TRANSACTIONS_PER_BATCH = 2000n; 

// Define fetch state interface to track in-progress fetches
interface FetchState {
  promise: Promise<Map<string, bigint>>;
  startTime: number;
  timeoutId?: NodeJS.Timeout;
}

// Cache to track in-progress fetches with timeout tracking
const activeTokenFetches = new Map<string, FetchState>();

// Set a maximum time for fetches to avoid hanging indefinitely
const FETCH_TIMEOUT_MS = 10 * 60 * 1000; // 10 minutes timeout for fetch operations

export class TokenHoldersCalculator {
    private agent: HttpAgent;
    private mainCanisterId: Principal;
    private mainCanisterActor: MainICRC2Service;

    constructor(mainCanisterId: Principal, agent: HttpAgent, resetActiveCalculations: boolean = false) {
        this.agent = agent;
        this.mainCanisterId = mainCanisterId;
        this.mainCanisterActor = Actor.createActor(icrc2IdlFactory, {
            agent: this.agent,
            canisterId: this.mainCanisterId,
        });
        
        if (resetActiveCalculations) {
            TokenHoldersCalculator.resetActiveCalculations();
        }
    }
    
    /**
     * Static method to reset all active token fetch calculations
     * Useful when starting a fresh calculation run or after application restart
     */
    public static resetActiveCalculations(): void {
        const count = activeTokenFetches.size;
        if (count > 0) {
            console.log(`[TokenHoldersCalculator] Clearing ${count} stale active token fetch calculations`);
            
            // Clear any timeouts to prevent memory leaks
            for (const [canisterId, fetchState] of activeTokenFetches.entries()) {
                if (fetchState.timeoutId) {
                    clearTimeout(fetchState.timeoutId);
                    console.log(`[TokenHoldersCalculator] Cleared timeout for fetch calculation: ${canisterId}`);
                }
            }
            
            // Clear the map
            activeTokenFetches.clear();
            console.log('[TokenHoldersCalculator] Active calculations map has been reset');
        } else {
            console.log('[TokenHoldersCalculator] No active calculations to reset');
        }
    }

    /**
     * Private helper method to clean up fetch state for a canister.
     * Clears the timeout and removes the entry from the active fetches map.
     */
    private cleanupFetchState(canisterId: string, reason: string): void {
        const fetchState = activeTokenFetches.get(canisterId);
        if (fetchState) {
            if (fetchState.timeoutId) {
                clearTimeout(fetchState.timeoutId);
            }
            activeTokenFetches.delete(canisterId);
            console.log(`[FetchCleanup] Cleaned up fetch state for ${canisterId}. Reason: ${reason}. Active fetches: ${activeTokenFetches.size}`);
        } else {
            console.log(`[FetchCleanup] Attempted to clean up fetch state for ${canisterId}, but no active fetch found. Reason: ${reason}.`);
        }
    }

    /**
     * Gets holder balances with caching in the database.
     * Prevents concurrent fetches for the same canister and implements a timeout.
     * @param forceRefresh Whether to force a refresh of the balances from the canister
     * @param maxAgeMinutes Maximum age of cached data in minutes
     * @returns Map of account IDs to balances
     */
    public async getHolderBalancesWithCache(forceRefresh: boolean = false, maxAgeMinutes: number = 1): Promise<Map<string, bigint>> {
        const canisterId = this.mainCanisterId.toText();
        const logPrefix = `[FetchManager canisterId=${canisterId}]`;
        console.log(`${logPrefix} Request received. Force refresh: ${forceRefresh}, Max age (min): ${maxAgeMinutes}. Active fetches: ${activeTokenFetches.size}`);

        if (activeTokenFetches.has(canisterId)) {
            const fetchState = activeTokenFetches.get(canisterId)!;
            const elapsedMs = Date.now() - fetchState.startTime;
            const elapsedSec = Math.round(elapsedMs / 1000);

            if (elapsedMs > FETCH_TIMEOUT_MS) {
                console.warn(`${logPrefix} Stale fetch detected. Existing fetch started ${elapsedSec}s ago (timeout is ${FETCH_TIMEOUT_MS / 1000}s). Cleaning up and starting new fetch.`);
                this.cleanupFetchState(canisterId, "Stale fetch detected by new request");
            } else {
                console.log(`${logPrefix} Active fetch found (started ${elapsedSec}s ago). Skipping new request. Active fetches: ${activeTokenFetches.size}`);
                return Promise.reject(new Error(`${logPrefix} Previous fetch still in progress. Started ${elapsedSec}s ago.`));
            }
        }

        console.log(`${logPrefix} Starting new fetch operation. Active fetches: ${activeTokenFetches.size + 1}`);
        const fetchStartTime = Date.now();
        const fetchPromise = this._fetchHolderBalances(forceRefresh, maxAgeMinutes);

        const timeoutId = setTimeout(() => {
            const currentFetchState = activeTokenFetches.get(canisterId);
            if (currentFetchState && currentFetchState.startTime === fetchStartTime) { // Ensure it's the same fetch we're timing out
                 console.warn(`${logPrefix} Fetch operation initiated at ${new Date(fetchStartTime).toISOString()} has timed out after ${FETCH_TIMEOUT_MS / 1000}s.`);
                 this.cleanupFetchState(canisterId, "Internal fetch timeout");
            } else if (currentFetchState) {
                console.log(`${logPrefix} Internal timeout fired, but current active fetch (startTime: ${currentFetchState.startTime}) is different from the one that set this timer (startTime: ${fetchStartTime}). Ignoring this timeout.`);
            } else {
                console.log(`${logPrefix} Internal timeout fired, but no active fetch found for this canister. It might have completed or been cleaned up already.`);
            }
        }, FETCH_TIMEOUT_MS);

        activeTokenFetches.set(canisterId, {
            promise: fetchPromise,
            startTime: fetchStartTime,
            timeoutId: timeoutId,
        });

        try {
            const result = await fetchPromise;
            console.log(`${logPrefix} Fetch completed successfully in ${Math.round((Date.now() - fetchStartTime) / 1000)}s.`);
            this.cleanupFetchState(canisterId, "Fetch completed successfully");
            return result;
        } catch (error) {
            console.error(`${logPrefix} Fetch failed after ${Math.round((Date.now() - fetchStartTime) / 1000)}s:`, error);
            this.cleanupFetchState(canisterId, "Fetch failed with error");
            throw error;
        }
    }
    // REMOVED EXTRA BRACE HERE
    /**
     * Internal method to fetch holder balances with caching
     */
    private async _fetchHolderBalances(forceRefresh: boolean = false, maxAgeMinutes: number = 1): Promise<Map<string, bigint>> {
        const canisterId = this.mainCanisterId.toText();
        
        try {
            // Import lazily to avoid circular dependencies
            
            
            // First, check only the freshness status without retrieving all balances
            if (!forceRefresh) {
                console.log(`Checking if we have fresh data for ${canisterId}`);
                const freshness = await checkTokenDataFreshness(canisterId, maxAgeMinutes);
                
                if (freshness && freshness.isFresh) {
                    console.log(`Fresh data available for ${canisterId}, last updated at ${freshness.lastFetchTime}, retrieving from DB`);
                    
                    // Since data is fresh, now retrieve the full balances
                    const dbResult = await getTokenHolderBalancesFromDB(canisterId);
                    
                    if (dbResult && dbResult.balances.size > 0) {
                        console.log(`Retrieved ${dbResult.balances.size} holder balances from DB`);
                        // Convert string balances back to bigint
                        const balancesMap = new Map<string, bigint>();
                        for (const [accountId, balance] of dbResult.balances.entries()) {
                            balancesMap.set(accountId, BigInt(balance));
                        }
                        return balancesMap;
                    }
                } else if (freshness) {
                    console.log(`Stale data for ${canisterId}, last updated at ${freshness.lastFetchTime}, needs refresh`);
                } else {
                    console.log(`No data found for ${canisterId}, will fetch from canister`);
                }
            } else {
                console.log(`Force refreshing balances for ${canisterId}`);
            }
            
            // Get existing data and last fetch index for incremental updates if available
            const freshness = await checkTokenDataFreshness(canisterId);
            const lastFetchIndex = freshness?.lastFetchIndex;
            
            // Initial call to get current metadata
            console.log(`Fetching initial transaction metadata for ${canisterId}...`);
            const initialResponse = await this.mainCanisterActor.get_transactions({ start: 0n, length: 1n });
            this.logInitialResponse(initialResponse); // Log details for debugging
            
            const logLength = initialResponse.log_length;
            // Save only logLength as the index, not first_index + logLength
            const currentLastIndex = logLength.toString();
            
            // Initialize with an empty map to avoid 'used before assigned' errors
            let balances: Map<string, bigint> = new Map<string, bigint>();
            
            // Check if we can do an incremental update
            if (lastFetchIndex && !forceRefresh) {
                // Convert stored logLength to bigint
                const lastLogLengthBigint = BigInt(lastFetchIndex);
                console.log(`Previous data exists with log length ${lastFetchIndex}, attempting incremental update`);
                
                // If current logLength is the same as what we've already processed,
                // then there are no new transactions
                if (logLength === lastLogLengthBigint) {
                    console.log(`No new transactions: current log length ${logLength} <= last processed ${lastLogLengthBigint}`);
                    // Just retrieve existing data since we're already caught up
                    const dbResult = await getTokenHolderBalancesFromDB(canisterId);
                    if (dbResult && dbResult.balances.size > 0) {
                        const balancesMap = new Map<string, bigint>();
                        for (const [accountId, balance] of dbResult.balances.entries()) {
                            balancesMap.set(accountId, BigInt(balance));
                        }
                        return balancesMap;
                    } else {
                        // Should not reach here normally, but as a fallback
                        balances = await this._getAllHolderBalancesInternal(); // Directly call internal method to avoid concurrency issues
                    }
                }
                // If logLength is greater, we need to fetch new transactions
                else if (logLength > lastLogLengthBigint) {
                    console.log(`New transactions available. Previous log length: ${lastFetchIndex}, Current log length: ${logLength}`);
                    console.log(`Will fetch new transactions and apply them to existing balances`);
                    
                    // Use existing balances as a base instead of starting from scratch
                    const dbResult = await getTokenHolderBalancesFromDB(canisterId);
                    if (!dbResult || dbResult.balances.size === 0) {
                        console.error(`No existing balances found in DB despite having last_fetch_index, falling back to full refresh`);
                        balances = await this.calculateBalances(await this.getTransactionsRange(initialResponse.first_index, initialResponse.first_index + logLength)); // Calculate balances from all transactions
                    } else {
                        // Convert from string to bigint for existing balances
                        balances = new Map<string, bigint>();
                        for (const [accountId, balance] of dbResult.balances.entries()) {
                            balances.set(accountId, BigInt(balance));
                        }
                        
                        // Calculate the range to fetch - we need transactions from lastLogLengthBigint to the current logLength
                        // For incremental updates, we fetch from the last processed index up to the current log length.
                        const startIndex = lastLogLengthBigint;
                        const endIndex = logLength;
                        
                        console.log(`Using existing balances from DB (${balances.size} holders) and fetching transactions in range [${startIndex}, ${endIndex})`);
                        const transactions = await this.getTransactionsRange(startIndex, endIndex);
                        console.log(`Fetched ${transactions.length} new transactions, applying to existing balances`);
                        
                        if (transactions.length === 0) {
                            console.log(`No new transactions to process. Skipping balance update.`);
                        } else {
                            this.updateBalancesWithTransactions(balances, transactions);
                        }
                    }
                } else {
                    // This is a fallback case that shouldn't normally be reached
                    // If we get here, it means the log length has decreased, which shouldn't happen
                    console.log(`Warning: Current log length ${logLength} is less than our last processed log length ${lastLogLengthBigint}. This shouldn't happen.`);
                    console.log(`Performing full balance calculation to recover from this inconsistency.`);
                    balances = await this._getAllHolderBalancesInternal(); // Directly call internal method to avoid concurrency issues
                }
            } else {
                // Need to do a full refresh
                console.log(`No existing data or force refresh requested, performing full balance calculation`);
                // Call internal method directly to avoid race condition with active fetches tracking
                balances = await this._getAllHolderBalancesInternal();
            }
            
            // Save to DB for future use
            const indexToSave = logLength.toString();
            console.log(`Saving ${balances.size} holder balances to DB with last index ${currentLastIndex}`);
            await saveTokenHolderBalancesToDB(canisterId, balances, indexToSave);
            

            return balances;
        } catch (error: any) {
            console.error(`Error during _fetchHolderBalances for ${canisterId} (attempting incremental or cached):`, error.message);
            // Fall back to full refresh if incremental update or cache check failed
            console.log(`Performing full balance calculation for ${canisterId} due to previous error: ${error.message}`);
            try {
                // Get fresh metadata FIRST to determine the range for full refresh and the correct 'last fetch index'
                const freshInitialResponse = await this.mainCanisterActor.get_transactions({ start: 0n, length: 1n });
                
                const fallbackBalances = await this.calculateBalances(await this.getTransactionsRange(freshInitialResponse.first_index, freshInitialResponse.first_index + freshInitialResponse.log_length)); // Calculate balances from all transactions
                console.log(`Successfully performed full balance calculation for ${canisterId} after fallback. Found ${fallbackBalances.size} holders.`);

                // Save only logLength as the index, not first_index + logLength
                const newLastFetchIndexForSave = freshInitialResponse.log_length.toString();

                console.log(`Saving ${fallbackBalances.size} holder balances (from full refresh) to DB for ${canisterId} with new last fetch index ${newLastFetchIndexForSave}.`);
                await saveTokenHolderBalancesToDB(canisterId, fallbackBalances, newLastFetchIndexForSave);
                return fallbackBalances;
            } catch (refreshError: any) {
                console.error(`Critical Error: Fallback getAllHolderBalances also failed for ${canisterId}:`, refreshError.message);
                throw refreshError; // Re-throw the critical error from full refresh
            }
        }
    }
    
    /**
     * Returns the map of active token fetches
     * Used by the controller to check if a fetch is already in progress
     */
    public getActiveFetches(): Map<string, FetchState> {
        return activeTokenFetches;
    }
    
    
    /**
     * Internal implementation of getAllHolderBalances that does the actual work
     * This is separated to facilitate proper concurrency control in the public method.
     */
    private async _getAllHolderBalancesInternal(): Promise<Map<string, bigint>> {
        const canisterIdText = this.mainCanisterId.toText();
        console.log(`Starting full balance calculation for canister: ${canisterIdText}`);
        console.log("Fetching all transactions (including from archives if necessary)...");
        const allTransactions: Transaction[] = [];

        // Step 1: Initial call to get metadata from main canister
        console.log(`Fetching initial metadata from main canister ${canisterIdText}...`);
        const initialResponse = await this.mainCanisterActor.get_transactions({ start: 0n, length: 1n });

        // Log initial response details for better diagnostics
        this.logInitialResponse(initialResponse);

        const logLength = initialResponse.log_length;
        const firstIndex = initialResponse.first_index;
        
        // Check for invalid firstIndex (max uint64 value) which indicates an error condition
        const MAX_UINT64 = 18446744073709551615n; // 2^64-1
        if (firstIndex === MAX_UINT64) {
            console.error("Invalid first_index value detected (max uint64). This is likely a canister error condition.");
            console.error("This can happen when a previous operation was interrupted or when state is inconsistent.");
            console.error("Attempting to recover by treating this as an empty transaction set.");
            return new Map();
        }

        if (logLength === 0n) {
            console.log("Log length is 0, no transactions to fetch.");
            return new Map();
        }

        // Step 2: Process archived transactions up to first_index
        console.log(`Processing archives up to index ${firstIndex}...`);
        for (const archive of initialResponse.archived_transactions) {
            const archiveCanisterId = (archive.callback as any)[0];
            const archiveRangeStart = archive.start;

            // Determine the effective range to fetch from this archive
            const fetchStartIndex = archiveRangeStart;
            // Use main canister's first_index as the target end index for archives
            const fetchEndIndex = firstIndex;

            console.log(`Archive ${archiveCanisterId.toText()}: Entry starts at ${archiveRangeStart}, Fetching configured range [${fetchStartIndex}, ${fetchEndIndex}) based on main canister first_index.`);

            if (fetchStartIndex >= fetchEndIndex) {
                console.log(`  Skipping archive, its reported start index (${fetchStartIndex}) is not less than the target end index (${fetchEndIndex}).`);
                continue;
            }

            // Create an actor for the archive canister
            const archiveActor = Actor.createActor<ArchiveICRC2Service>(archiveCanisterIdlFactory, {
                agent: this.agent,
                canisterId: archiveCanisterId,
            });

            let currentGlobalIndex = fetchStartIndex;
            while (currentGlobalIndex < fetchEndIndex) {
                const remainingInArchive = fetchEndIndex - currentGlobalIndex;
                let lengthToFetch = remainingInArchive < MAX_TRANSACTIONS_PER_BATCH ? remainingInArchive : MAX_TRANSACTIONS_PER_BATCH;

                if (lengthToFetch <= 0n) {
                    console.warn(`  Calculated lengthToFetch is ${lengthToFetch} at index ${currentGlobalIndex}, but fetchEndIndex is ${fetchEndIndex}. Breaking loop.`);
                    break;
                }

                console.log(`  Fetching batch from archive ${archiveCanisterId.toText()}: start=${currentGlobalIndex}, length=${lengthToFetch}`);
                try {
                    const archiveResponse : ArchiveResponse = await archiveActor.get_transactions({ start: currentGlobalIndex, length: lengthToFetch });

                    if (Array.isArray(archiveResponse.transactions)) {
                        const batchTransactions = archiveResponse.transactions;
                        console.log(`    Fetched ${batchTransactions.length} transactions.`);
                        allTransactions.push(...batchTransactions);

                        // Increment strictly by requested length to ensure progress even if fewer returned (might indicate end)
                        currentGlobalIndex += lengthToFetch;

                        // Safety break if archive returns 0 unexpectedly
                        if (batchTransactions.length === 0 && lengthToFetch > 0n) {
                            console.warn(`    Warning: Archive ${archiveCanisterId.toText()} returned 0 transactions when ${lengthToFetch} were requested starting at ${currentGlobalIndex - lengthToFetch}. Stopping fetch for this archive.`);
                            break;
                        }
                    } else {
                        console.error(`    Error: Unexpected response structure from archive ${archiveCanisterId.toText()}:`, archiveResponse);
                        break;
                    }
                } catch (error) {
                    console.error(`    Error fetching/decoding batch from archive ${archiveCanisterId.toText()}: start=${currentGlobalIndex}, length=${lengthToFetch}`, error);
                    throw error;
                }
            }
            console.log(`  Finished fetching from archive ${archiveCanisterId.toText()}. Total transactions after this archive: ${allTransactions.length}`);
        }

        // Step 3: Fetch transactions from the main canister
        const mainCanisterFetchStart = firstIndex;
        const mainCanisterFetchEnd = logLength;

        console.log(`Processing main canister transactions from index ${mainCanisterFetchStart} to ${mainCanisterFetchEnd}...`);

        if (mainCanisterFetchStart < mainCanisterFetchEnd) {
            let currentGlobalIndex = mainCanisterFetchStart;
            while (currentGlobalIndex < mainCanisterFetchEnd) {
                const remainingInMain = mainCanisterFetchEnd - currentGlobalIndex;
                const lengthToFetch = remainingInMain < MAX_TRANSACTIONS_PER_BATCH ? remainingInMain : MAX_TRANSACTIONS_PER_BATCH;

                if (lengthToFetch <= 0n) { // Should not happen if mainCanisterFetchStart < mainCanisterFetchEnd
                    console.warn(`  Main canister calculated lengthToFetch is ${lengthToFetch}. Breaking loop.`);
                    break;
                }

                console.log(`  Fetching batch from main canister: start=${currentGlobalIndex}, length=${lengthToFetch}`);
                try {
                    const mainResponse = await this.mainCanisterActor.get_transactions({ start: currentGlobalIndex, length: lengthToFetch });
                    const batchTransactions = mainResponse.transactions;
                    console.log(`    Fetched ${batchTransactions.length} transactions.`);
                    allTransactions.push(...batchTransactions);

                    // Increment strictly by requested length
                    currentGlobalIndex += lengthToFetch;

                    if (batchTransactions.length === 0 && lengthToFetch > 0n) {
                        console.warn(`    Warning: Main canister returned 0 transactions when ${lengthToFetch} were requested starting at ${currentGlobalIndex - lengthToFetch}. Stopping fetch.`);
                        break; // Avoid infinite loops
                    }
                } catch (error) {
                    console.error(`  Error fetching batch from main canister: start=${currentGlobalIndex}, length=${lengthToFetch}`, error);
                    throw error;
                }
            }
        } else {
            console.log(`  Skipping main canister fetch, firstIndex (${mainCanisterFetchStart}) is not less than logLength (${mainCanisterFetchEnd}).`);
        }

        console.log(`Finished fetching all transactions. Total count: ${allTransactions.length}. Expected count from initial response: ${logLength}.`);
        if (BigInt(allTransactions.length) !== logLength) {
            console.warn(`Warning: Final fetched count (${allTransactions.length}) does not match initial log_length (${logLength}). This might indicate issues during fetching or inconsistent canister reporting.`);
        }

        // Step 4: Calculate balances
        console.log("Calculating final balances...");
        const balances = this.calculateBalances(allTransactions);
        console.log(`Finished calculating balances. Found ${balances.size} unique holders (with non-zero final balance).`);
        return balances;
    }

    private logInitialResponse(response: GetTransactionsResponse): void {
         console.log("--- Initial Main Canister Response --- ");
         console.log(`  Log Length: ${response.log_length?.toString() ?? 'N/A'}`);
         console.log(`  First Index: ${response.first_index?.toString() ?? 'N/A'}`);
         console.log(`  Archived Transactions (${response.archived_transactions?.length ?? 0} entries):`);
         if (response.archived_transactions) {
             response.archived_transactions.forEach((archive, index: number) => {
                 console.log(`    Archive[${index}]:`);
                 console.log(`      Start Index: ${archive.start?.toString() ?? 'N/A'}`);
                 console.log(`      Reported Length: ${archive.length?.toString() ?? 'N/A'}`);
                 if (archive.start !== undefined && archive.length !== undefined) {
                    const start = BigInt(archive.start);
                    const length = BigInt(archive.length);
                    console.log(`      Absolute Range Covered by Entry: [${start.toString()}, ${(start + length).toString()})`);
                 } else {
                    console.log(`      Absolute Range Covered by Entry: N/A`);
                 }
             });
         }
         console.log("------------------------------------");
    }

    /**
     * Fetches transactions within a specific range
     * @param startIndex The starting index to fetch from (inclusive)
     * @param endIndex The ending index to fetch up to (exclusive)
     * @returns Array of transactions
     */
    private async getTransactionsRange(startIndex: bigint, endIndex: bigint): Promise<Transaction[]> {
        console.log(`Fetching transaction range [${startIndex}, ${endIndex})...`);
        const allTransactions: Transaction[] = [];
        
        // Step 1: Get initial metadata to understand where transactions are stored
        const initialResponse = await this.mainCanisterActor.get_transactions({ start: 0n, length: 1n });
        const firstIndex = initialResponse.first_index;
        
        // Check for invalid firstIndex (max uint64 value) which indicates an error condition
        const MAX_UINT64 = 18446744073709551615n; // 2^64-1
        if (firstIndex === MAX_UINT64) {
            console.error("getTransactionsRange: Invalid first_index value detected (max uint64). This is likely a canister error condition.");
            console.error("This can happen when a previous operation was interrupted or when state is inconsistent.");
            return []; // Return empty array to avoid processing with invalid index
        }
        
        // Step 2: Process archived transactions if needed
        if (startIndex < firstIndex) {
            console.log(`Some requested transactions are in archives (start: ${startIndex}, main first: ${firstIndex})`);
            for (const archive of initialResponse.archived_transactions) {
                const archiveCanisterId = (archive.callback as any)[0];
                const archiveRangeStart = archive.start;
                const archiveRangeEnd = archiveRangeStart + archive.length;
                
                // Check if this archive contains transactions we need
                if (archiveRangeEnd <= startIndex || archiveRangeStart >= endIndex) {
                    continue; // This archive is outside our range of interest
                }
                
                // Determine the effective range to fetch from this archive
                const fetchStartIndex = BigInt(Math.max(Number(startIndex), Number(archiveRangeStart)));
                const fetchEndIndex = BigInt(Math.min(Number(endIndex), Number(archiveRangeEnd)));
                
                console.log(`Fetching from archive ${archiveCanisterId.toText()}: [${fetchStartIndex}, ${fetchEndIndex})`);
                
                const archiveActor = Actor.createActor<ArchiveICRC2Service>(archiveCanisterIdlFactory, {
                    agent: this.agent,
                    canisterId: archiveCanisterId,
                });
                
                let currentIndex = fetchStartIndex;
                while (currentIndex < fetchEndIndex) {
                    const batchSize = fetchEndIndex - currentIndex < MAX_TRANSACTIONS_PER_BATCH ? 
                        fetchEndIndex - currentIndex : MAX_TRANSACTIONS_PER_BATCH;
                        
                    try {
                        const archiveResponse = await archiveActor.get_transactions({ 
                            start: currentIndex, 
                            length: batchSize 
                        });
                        
                        if (Array.isArray(archiveResponse.transactions)) {
                            console.log(`  Fetched ${archiveResponse.transactions.length} transactions from archive`);
                            allTransactions.push(...archiveResponse.transactions);
                        }
                        
                        currentIndex += batchSize;
                    } catch (error) {
                        console.error(`Error fetching from archive:`, error);
                        throw error;
                    }
                }
            }
        }
        
        // Step 3: Process transactions from main canister if needed
        if (endIndex > firstIndex) {
            // Adjust range to only fetch what's available in main canister
            const mainStartIndex = startIndex > firstIndex ? startIndex : firstIndex;
            let currentIndex = mainStartIndex;
            
            while (currentIndex < endIndex) {
                const batchSize = endIndex - currentIndex < MAX_TRANSACTIONS_PER_BATCH ?
                    endIndex - currentIndex : MAX_TRANSACTIONS_PER_BATCH;
                    
                try {
                    const mainResponse = await this.mainCanisterActor.get_transactions({
                        start: currentIndex,
                        length: batchSize
                    });
                    
                    console.log(`  Fetched ${mainResponse.transactions.length} transactions from main canister`);
                    allTransactions.push(...mainResponse.transactions);
                    
                    currentIndex += batchSize;
                } catch (error) {
                    console.error(`Error fetching from main canister:`, error);
                    throw error;
                }
            }
        }
        
        console.log(`Total transactions fetched for range [${startIndex}, ${endIndex}): ${allTransactions.length}`);
        return allTransactions;
    }
    
    /**
     * Updates an existing balance map with transactions
     * @param balances The balance map to update (modified in place)
     * @param transactions The transactions to apply
     */
    private updateBalancesWithTransactions(balances: Map<string, bigint>, transactions: Transaction[]): void {
        console.log(`Updating balances with ${transactions.length} transactions`);
        
        for (const tx of transactions) {
            if (tx.transfer && tx.transfer.length > 0) {
                const transfer = tx.transfer[0];
                if (!transfer || typeof transfer !== 'object') continue;
                
                const fromAccount = transfer.from?.owner?.toText();
                const toAccount = transfer.to?.owner?.toText();
                const amount = transfer.amount;
                
                // Extract fee safely
                let fee = 0n;
                if (transfer.fee && transfer.fee.length > 0 && transfer.fee[0] !== undefined) {
                    fee = transfer.fee[0];
                }
                
                // Update sender balance (deduct amount and fee)
                if (fromAccount && amount > 0n) {
                    const currentFromBalance = balances.get(fromAccount) || 0n;
                    balances.set(fromAccount, currentFromBalance - amount - fee);
                }
                
                // Update recipient balance (add amount only)
                if (toAccount && amount > 0n) {
                    const currentToBalance = balances.get(toAccount) || 0n;
                    balances.set(toAccount, currentToBalance + amount);
                }
            } else if (tx.approve && tx.approve.length > 0) {
                const approve = tx.approve[0];
                if (!approve || typeof approve !== 'object') continue;
                
                const fromAccount = approve.from?.owner?.toText();
                
                // Extract fee safely
                let fee = 0n;
                if (approve.fee && approve.fee.length > 0 && approve.fee[0] !== undefined) {
                    fee = approve.fee[0];
                }
                
                // Update approver's balance (deduct fee only)
                if (fromAccount && fee > 0n) {
                    const currentFromBalance = balances.get(fromAccount) || 0n;
                    balances.set(fromAccount, currentFromBalance - fee);
                }
            } else if (tx.mint && tx.mint.length > 0) {
                const mint = tx.mint[0];
                if (!mint || typeof mint !== 'object') continue;
                
                const toAccount = mint.to?.owner?.toText();
                const amount = mint.amount;
                
                // Update recipient balance for mint
                if (toAccount && amount > 0n) {
                    const currentToBalance = balances.get(toAccount) || 0n;
                    balances.set(toAccount, currentToBalance + amount);
                }
            } else if (tx.burn && tx.burn.length > 0) {
                const burn = tx.burn[0];
                if (!burn || typeof burn !== 'object') continue;
                
                const fromAccount = burn.from?.owner?.toText();
                const amount = burn.amount;
                
                // Update sender balance for burn
                if (fromAccount && amount > 0n) {
                    const currentFromBalance = balances.get(fromAccount) || 0n;
                    balances.set(fromAccount, currentFromBalance - amount);
                }
            }
        }
        
        console.log(`Balance update complete, ${balances.size} accounts affected`);
    }
    
    private calculateBalances(transactions: Transaction[]): Map<string, bigint> {
        const balances = new Map<string, bigint>();

        for (const tx of transactions) {
            if (tx.transfer && tx.transfer.length > 0) {
                const transfer = tx.transfer[0];
                if (!transfer || typeof transfer !== 'object') continue; // Basic validation
                const fromAccount = transfer.from?.owner?.toText();
                const toAccount = transfer.to?.owner?.toText();
                const amount = transfer.amount;
                // Safely extract fee from the optional array
                let fee = 0n;
                if (transfer.fee && transfer.fee.length > 0 && transfer.fee[0] !== undefined) {
                    fee = transfer.fee[0];
                }

                if (fromAccount && amount > 0n) {
                    // Deduct amount plus fee from sender
                    const currentFromBalance = balances.get(fromAccount) || 0n;
                    balances.set(fromAccount, currentFromBalance - amount - fee);
                }
                if (toAccount && amount > 0n) {
                    // Recipient gets only the amount (not the fee)
                    const currentToBalance = balances.get(toAccount) || 0n;
                    balances.set(toAccount, currentToBalance + amount);
                }
            } else if (tx.approve && tx.approve.length > 0) {
                const approve = tx.approve[0];
                if (!approve || typeof approve !== 'object') continue;
                
                // Get the fromAccount (the account giving approval)
                const fromAccount = approve.from?.owner?.toText();
                
                // Extract fee from the optional array
                let fee = 0n;
                if (approve.fee && approve.fee.length > 0 && approve.fee[0] !== undefined) {
                    fee = approve.fee[0];
                }
                
                // Deduct fee from the approver's account if a fee is present
                if (fromAccount && fee > 0n) {
                    const currentFromBalance = balances.get(fromAccount) || 0n;
                    balances.set(fromAccount, currentFromBalance - fee);
                }
            } else if (tx.mint && tx.mint.length > 0) {
                const mint = tx.mint[0];
                if (!mint || typeof mint !== 'object') continue;
                const toAccount = mint.to?.owner?.toText();
                const amount = mint.amount;

                if (toAccount && amount > 0n) {
                    const currentToBalance = balances.get(toAccount) || 0n;
                    balances.set(toAccount, currentToBalance + amount);
                }
            } else if (tx.burn && tx.burn.length > 0) {
                const burn = tx.burn[0];
                if (!burn || typeof burn !== 'object') continue;
                const fromAccount = burn.from?.owner?.toText();
                const amount = burn.amount;

                if (fromAccount && amount > 0n) {
                    const currentFromBalance = balances.get(fromAccount) || 0n;
                    balances.set(fromAccount, currentFromBalance - amount);
                }
            }
        }

        // Clean up zero balances for clarity, although filtering happens in main.ts
        const positiveBalances = new Map<string, bigint>();
        for (const [holder, balance] of balances.entries()) {
            if (balance !== 0n) { // Keep negative balances for now, filter positives in main
                positiveBalances.set(holder, balance);
            }
        }
        return positiveBalances; // Return map potentially including negatives
    }
}
