import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import https from 'https';

export class HttpClientService {
  private client: AxiosInstance;
  private readonly defaultTimeout = 30000; // 30 seconds
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000; // 1 second

  constructor(baseURL?: string, config: AxiosRequestConfig = {}) {
    this.client = axios.create({
      baseURL,
      timeout: this.defaultTimeout,
      httpsAgent: new https.Agent({  
        rejectUnauthorized: false // Note: Only use this in development
      }),
      ...config,
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        console.log(`[HTTP] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[HTTP] Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`[HTTP] ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        if (error.response) {
          console.error(`[HTTP] Response Error: ${error.response.status} ${error.response.statusText}`);
        } else if (error.request) {
          console.error('[HTTP] Request Error:', error.message);
        } else {
          console.error('[HTTP] Error:', error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  private async retryRequest<T>(
    requestFn: () => Promise<AxiosResponse<T>>,
    retries = this.maxRetries
  ): Promise<AxiosResponse<T>> {
    try {
      return await requestFn();
    } catch (error) {
      if (retries === 0) throw error;
      
      console.log(`[HTTP] Retrying request... (${this.maxRetries - retries + 1}/${this.maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      return this.retryRequest(requestFn, retries - 1);
    }
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.retryRequest(() => this.client.get<T>(url, config));
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.retryRequest(() => this.client.post<T>(url, data, config));
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.retryRequest(() => this.client.put<T>(url, data, config));
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.retryRequest(() => this.client.delete<T>(url, config));
    return response.data;
  }
}
