import { HttpClientService } from './http-client';

export class ExternalApiService {
  private httpClient: HttpClientService;

  constructor() {
    this.httpClient = new HttpClientService();
  }

  async getTokenHolders(canisterId: string): Promise<any> {
    try {
      const holders = await this.httpClient.post<any>(
        `https://open-api.icexplorer.io/api/holder/token`,
        {
          page: 1,
          size: 1,
          ledgerId: canisterId
        },
        {
          headers: {
            'Accept': 'application/json'
          },
        }
      );
      return holders;
    } catch (error) {
      console.error(`Error fetching token holders for ${canisterId}:`, error);
      return [];
    }
  }

  // Add more external API methods here as needed
}
