import { WebSocket, WebSocketServer } from 'ws';
import { BuyMessageData, NewPoolMessageData, RobertChoiceMessageData } from '../types';

export class WebSocketService {
    private static instance: WebSocketService;
    private wss: WebSocketServer | null = null;
    private clients: Set<WebSocket> = new Set();
    private pingInterval: NodeJS.Timeout | null = null;

    private constructor() { }

    public static getInstance(): WebSocketService {
        if (!WebSocketService.instance) {
            WebSocketService.instance = new WebSocketService();
        }
        return WebSocketService.instance;
    }

    public initialize() {
        if (this.wss) {
            console.log('WebSocket server already initialized');
            return; // Already initialized
        }

        try {
            console.log('Starting WebSocket server on port 2137...');
            this.wss = new WebSocketServer({ port: 2137 });
            console.log('WebSocket server started successfully');

            this.wss.on('connection', (ws, req) => {
                console.log('New client connected from:', req.socket.remoteAddress);
                this.clients.add(ws);

                ws.on('close', (code, reason) => {
                    console.log('Client disconnected. Code:', code, 'Reason:', reason.toString());
                    this.clients.delete(ws);
                });

                ws.on('error', (error) => {
                    console.error('Client connection error:', error);
                });
            });

            this.wss.on('error', (error: any) => {
                console.error('WebSocket server error:', error);
            });

            this.wss.on('listening', () => {
                console.log('WebSocket server is listening');
            });


        } catch (error) {
            console.error('Failed to initialize WebSocket server:', error);
            throw error;
        }
    }

    public broadcast(message: BuyMessageData | NewPoolMessageData | RobertChoiceMessageData) {
        // Custom JSON serializer to handle BigInt values
        const messageStr = JSON.stringify(message, (_key, value) => {
            if (typeof value === 'bigint') {
                return value.toString();
            }
            return value;
        });

        // Create a safe version for logging (without image data)
        const safeMessage = { ...message };
        if ('image' in safeMessage && safeMessage.image) {
            safeMessage.image = `[IMAGE_DATA_${safeMessage.image.length}_CHARS]`;
        }

        console.log(`Broadcasting message to ${this.clients.size} clients:`, JSON.stringify(safeMessage, (_key, value) => {
            if (typeof value === 'bigint') {
                return value.toString();
            }
            return value;
        }));
        this.clients.forEach(async (client) => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(messageStr);
                if (message.type === 'robert_choice') {
                    console.log('Robert choice message sent to client');
                } else {
                    console.log('Buy message sent to client');
                }
            } else {
                console.log('Client not ready, state:', client.readyState);
            }
        });
    }

    public close() {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = null;
        }
        if (this.wss) {
            console.log('Closing WebSocket server...');
            this.wss.close(() => {
                console.log('WebSocket server closed');
                this.wss = null;
            });
        }
    }
}
