import { getTokenMetrics, updateTrendingMetrics } from './database/db';
import { TrendingMetric } from '../types';

export const updateTrending = async () => {
    try {
        const updateTrendingStart = Date.now();
        const tokenMetrics: TrendingMetric[] = await getTokenMetrics();

        // Filter out duplicates by choosing the ones with the most socials
        const uniqueMetrics = tokenMetrics.reduce((acc: TrendingMetric[], metric: TrendingMetric) => {
            const existing = acc.find(item => item.address === metric.address);
            if (!existing || (metric.socials && metric.socials.length > (existing.socials ? existing.socials.length : 0))) {
                return acc.filter(item => item.address !== metric.address).concat(metric);
            }
            return acc;
        }, []);

        // Sort by a combination of volumeUSD1d and priceUSDChange
        const sortedMetrics = uniqueMetrics.sort((a, b) => {
            const scoreA = a.volume_usd_1d * 0.5 + a.price_usd_change * 0.5;
            const scoreB = b.volume_usd_1d * 0.5 + b.price_usd_change * 0.5;
            return scoreB - scoreA;
        }).slice(0, 10);
        await updateTrendingMetrics(sortedMetrics);
        const updateTrendingEnd = Date.now();
        console.log(`updateTrending took ${updateTrendingEnd - updateTrendingStart}ms`);
    } catch (error) {
        console.error('Failed to update trending metrics:', error);
    }
};