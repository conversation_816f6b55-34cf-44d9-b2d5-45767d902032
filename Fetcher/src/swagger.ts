export const swaggerDocument = {
  openapi: '3.0.0',
  info: {
    title: 'ICP Tools API',
    version: '1.0.0',
    description: 'API for interacting with Internet Computer Protocol',
  },
  servers: [
    {
      url: 'http://localhost:3000',
      description: 'Local development server',
    },
  ],
  paths: {
    '/api/tokens/{address}': {
      get: {
        summary: 'Get token information by address',
        parameters: [
          {
            name: 'address',
            in: 'path',
            required: true,
            schema: {
              type: 'string',
            },
            description: 'The token canister address',
          },
        ],
        responses: {
          '200': {
            description: 'Token information retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    address: {
                      type: 'string',
                      description: 'Token canister address',
                    },
                    name: {
                      type: 'string',
                      description: 'Token name',
                    },
                    symbol: {
                      type: 'string',
                      description: 'Token symbol',
                    },
                    decimals: {
                      type: 'number',
                      description: 'Token decimals',
                    },
                    totalSupply: {
                      type: 'string',
                      description: 'Total supply of the token',
                    },
                    standard: {
                      type: 'string',
                      description: 'Token standard (ICRC-1, ICRC-2, or DIP20)',
                      enum: ['ICRC-1', 'ICRC-2', 'DIP20'],
                    },
                    owner: {
                      type: 'string',
                      description: 'Principal ID of the token owner',
                    },
                    poolAddresses: {
                      type: 'array',
                      items: {
                        type: 'string',
                      },
                      description: 'List of pool canister IDs containing this token',
                    },
                    timestamp: {
                      type: 'string',
                      format: 'date-time',
                      description: 'Timestamp of when the data was fetched',
                    },
                  },
                  required: [
                    'address',
                    'name',
                    'symbol',
                    'decimals',
                    'totalSupply',
                    'standard',
                    'owner',
                    'poolAddresses',
                    'timestamp',
                  ],
                },
              },
            },
          },
          '400': {
            description: 'Invalid address format',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/tokens/{address}/pools': {
      get: {
        summary: 'Get all pools containing the specified token',
        parameters: [
          {
            name: 'address',
            in: 'path',
            required: true,
            schema: {
              type: 'string',
            },
            description: 'The token canister address',
          },
        ],
        responses: {
          '200': {
            description: 'Pool information retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      key: {
                        type: 'string',
                        description: 'Unique pool identifier',
                      },
                      fee: {
                        type: 'string',
                        description: 'Pool fee',
                      },
                      tickSpacing: {
                        type: 'string',
                        description: 'Pool tick spacing',
                      },
                      canisterId: {
                        type: 'string',
                        description: 'Pool canister ID',
                      },
                      token0: {
                        type: 'object',
                        properties: {
                          address: {
                            type: 'string',
                            description: 'Token0 address',
                          },
                          standard: {
                            type: 'string',
                            description: 'Token0 standard',
                          },
                        },
                      },
                      token1: {
                        type: 'object',
                        properties: {
                          address: {
                            type: 'string',
                            description: 'Token1 address',
                          },
                          standard: {
                            type: 'string',
                            description: 'Token1 standard',
                          },
                        },
                      },
                    },
                    required: [
                      'key',
                      'fee',
                      'tickSpacing',
                      'canisterId',
                      'token0',
                      'token1',
                    ],
                  },
                },
              },
            },
          },
          '400': {
            description: 'Invalid address format',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: {
                      type: 'string',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};
