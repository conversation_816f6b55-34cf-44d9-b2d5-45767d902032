import { Principal } from "@dfinity/principal";

export interface NewPoolMessageData {
  canisterId: string;
  token0: BuyTokenData;
  token1: BuyTokenData;
  dex: string;
  type: string;
}

export interface TokenMetadata {
  name: string;
  symbol: string;
  decimals: number;
  totalSupply: bigint;
  owner: Principal;
}

export interface DIP20TokenInfo {
  metadata: TokenMetadata;
}

export interface TokenData {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  totalSupply: string;
  standard: string;
  owner: string;
  poolAddresses: string[];
}

export interface BuyTokenData {
  address: string;
  name: string;
  symbol: string;
  amount?: number;
  priceUSD?: number;
  pricePairToken?: number;
}

export interface BuyMessageData {
  spentToken: BuyTokenData;
  gotToken: BuyTokenData;
  pairAddress: string;
  spentDollars: number;
  holderIncrease?: string;
  holderWallet?: string;
  marketcap: number;
  dex: string;
  type: string;
  icpCollected?: number; // only for bob.fun
  timestamp?: string;
  holderPercentageOwned?: number;
}

export interface RobertTokenInfo {
  decimals: number;
  totalSupply: string;
  standard: string;
  owner: string;
  poolAddresses: string[];
  // Market metrics from token_metrics table
  price_usd?: number;
  price_usd_change?: number;
  volume_usd_1d?: number;
  volume_usd_7d?: number;
  total_volume_usd?: number;
  fees_usd?: number;
  tx_count?: number;
}

export interface SocialLinks {
  telegram?: string;
  twitter?: string;
  website?: string;
  openChat?: string;
}

export interface RobertChoiceMessageData {
  address: string;
  ticker: string;
  name: string;
  description: string;
  created_at_token: Date;
  created_by: string;
  creatorBalance?: string;
  zero_point_one_icp_tokens_out?: bigint;
  image: string;
  type: string;
  socialLinks?: SocialLinks; // Social media links from Bob canister
  tokenInfo?: RobertTokenInfo; // Extended token information with market data
}

export interface ICPResult<T> {
  ok?: T;
  err?: any;
}

export interface MetadataMap {
  [key: string]: any;
}

export interface StorageResponse {
  totalElements: number;
  content: any[];
}

export interface TokenMetrics {
  address: string;
  volumeUSD1d: number;
  volumeUSD7d: number;
  totalVolumeUSD: number;
  volumeUSD: number;
  feesUSD: number;
  priceUSDChange: number;
  txCount: number;
  priceUSD: number;
}

export interface TrendingMetric {
  address: string;
  name: string;
  symbol: string;
  volume_usd_1d: number;
  price_usd: number;
  price_usd_change: number;
  socials: any;
}

// export interface BobBuyMessageData {
//   spentToken: BuyTokenData;
//   gotToken: BuyTokenData;
//   spentDollars: number;
//   // holderIncrease?: string;
//   // holderWallet?: string;
//   // marketcap: number;
//   dex: string;
//   type: string;
// }
// Interface for Bob order data
export interface BobOrder {
  ts: bigint;
  from: Principal;
  amount_e8s: bigint;
  order_type: { Buy: null } | { Sell: null };
}

// Interface for Bob candle data
export interface BobCandle {
  low: number;
  high: number;
  close: number;
  open: number;
  time: bigint;
}