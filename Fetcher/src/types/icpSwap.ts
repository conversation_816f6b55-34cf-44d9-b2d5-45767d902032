import { Principal } from '@dfinity/principal';

export interface Token {
  address: string;
  standard: string;
}

export interface PoolData {
  fee: string;
  key: string;
  tickSpacing: string;
  token0: Token;
  token1: Token;
  canisterId: string;
}

export type Error = 
  | { CommonError: null }
  | { InternalError: string }
  | { UnsupportedToken: string }
  | { InsufficientFunds: null };

export type Result_3 = {
  ok: Array<PoolData>;
  err: Error;
};
