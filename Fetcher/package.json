{"name": "icp-tools", "version": "1.0.0", "description": "ICP Tools API", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn src/index.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@dfinity/agent": "^0.20.2", "@dfinity/candid": "^0.20.2", "@dfinity/principal": "^0.20.2", "@types/pg": "^8.11.10", "axios": "^1.7.9", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "pg": "^8.13.1", "ssl-root-cas": "^1.3.1", "swagger-ui-express": "^5.0.0", "tsx": "^4.7.0", "ws": "^8.16.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/swagger-ui-express": "^4.1.6", "@types/ws": "^8.5.10", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}