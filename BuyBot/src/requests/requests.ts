import axios from 'axios';
import { getTokenInfoFromDB, checkTokenHoldersFreshness, getTokenHoldersFromDB, getPoolsForToken } from '../db/queries';
import { logInfo } from '../libs/logger';

export interface TokenInfo {
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  totalSupply: string;
  standard: string;
  owner: string;
  poolAddresses?: string[] | undefined;
  holders?: number;
  timestamp?: string;
}
export interface TokenHoldersResponse {
  holders: {
    accountId: string;
    balance: string;
    formattedBalance?: string;
    percentage?: string;
    isPool?: boolean;
    poolName?: string;
  }[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalHolders: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  token?: {
    address: string;
    name: string;
    symbol: string;
    decimals: number;
    totalSupply: string;
  };
}
const FETCHER_HOST = process.env.FETCHER_HOST || 'localhost';
const FETCHER_PORT = process.env.FETCHER_PORT || 3000;

export const fetchTokenInfoFetcher = async (address: string): Promise<TokenInfo | undefined> => {
  try {
    const response = await axios.get(`http://${FETCHER_HOST}:${FETCHER_PORT}/api/tokens/${address}`);
    if (response.data && response.data.error) {
      console.error('Error in token info response:', response.data.error);
      return undefined;
    }
    return response.data;
  } catch (error) {
    console.error('Error fetching token info:', error);
    throw error;
  }
};

const castBobToTokenInfo = (bobTokenInfo: any): TokenInfo => {
  return {
    address: bobTokenInfo.liquidity_pool.token_id,
    name: bobTokenInfo.token_info.name,
    symbol: bobTokenInfo.token_info.ticker,
    decimals: 8,
    totalSupply: "100000000000000000",
    standard: 'BOB-ICRC-2',
    owner: bobTokenInfo.token_info.created_by,
    poolAddresses: undefined,
    holders: 0,
  };
}

export const fetchBobTokenInfo = async (address: BigInt): Promise<TokenInfo | undefined> => {
  try {
    const dbToken = await getTokenInfoFromDB(String(address));

    if (dbToken) {
      // Check if token was updated in the last 5 minutes
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      if (dbToken.updatedAt > fiveMinutesAgo) {
        return {
          address: dbToken.address,
          decimals: dbToken.decimals,
          name: dbToken.name,
          symbol: dbToken.symbol,
          totalSupply: dbToken.totalSupply,
          standard: dbToken.standard,
          owner: dbToken.owner
        };
      }
    }
    const response = await axios.get(`http://${FETCHER_HOST}:${FETCHER_PORT}/api/tokens/bob/${address}`);
    if (response.data && response.data.error) {
      console.error('Error in token info response:', response.data.error);
      return undefined;
    }
    return castBobToTokenInfo(response.data);
  } catch (error) {
    console.error('Error fetching token info:', error);
    throw error;
  }
};
export const fetchTokenInfo = async (address: string): Promise<TokenInfo | undefined> => {
  try {
    // First try to get token from database
    const dbToken = await getTokenInfoFromDB(address);

    if (dbToken) {
      // Check if token was updated in the last 5 minutes
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      if (dbToken.updatedAt > fiveMinutesAgo) {
        return {
          address: dbToken.address,
          decimals: dbToken.decimals,
          name: dbToken.name,
          symbol: dbToken.symbol,
          totalSupply: dbToken.totalSupply,
          standard: dbToken.standard,
          owner: dbToken.owner
        };
      }
    }

    // If token not in DB or older than 5 minutes, fetch from Fetcher service
    try {
      return await fetchTokenInfoFetcher(address);
    } catch (error) {
      console.error('Error in fetchTokenInfoFetcher:', error);
      // FALLBACK TO DB
      if (dbToken) {
        return {
          address: dbToken.address,
          decimals: dbToken.decimals,
          name: dbToken.name,
          symbol: dbToken.symbol,
          totalSupply: dbToken.totalSupply,
          standard: dbToken.standard,
          owner: dbToken.owner
        };
      }
    }
  } catch (error) {
    console.error('Error in fetchTokenInfo:', error);
  }
};

export const fetchTokenHolders = async (address: string, page: number = 1, limit: number = 20, forceUseDb: boolean = false): Promise<TokenHoldersResponse | undefined> => {
  try {
    // First, fetch token info to get decimals and total supply
    const tokenInfo = await fetchTokenInfo(address);
    if (!tokenInfo) {
      console.error('Failed to fetch token info for', address);
      return undefined;
    }

    // First check if we have any data in the database
    let responseData: Record<string, string> | undefined;
    const freshness = await checkTokenHoldersFreshness(address, 5);

    if (forceUseDb) {
      // For button interactions, always try to use DB data first, even if it's not fresh
      logInfo('fetchTokenHolders', `Attempting to use DB data for button interaction (${address}) regardless of freshness`);
      responseData = await getTokenHoldersFromDB(address);

      // If we got data from DB, use it and don't hit the Fetcher API
      if (responseData && Object.keys(responseData).length > 0) {
        logInfo('fetchTokenHolders', `Using existing DB data for ${address}, last updated at ${freshness?.lastFetchTime || 'unknown'}`);
      } else {
        logInfo('fetchTokenHolders', `No DB data found for ${address} despite forceUseDb, will try Fetcher API`);
      }
    } else if (freshness?.isFresh) {
      // Normal flow - use fresh data from DB if available
      logInfo('fetchTokenHolders', `Using fresh holder data from DB for ${address} (last updated ${freshness.lastFetchTime})`);
      responseData = await getTokenHoldersFromDB(address);
    }

    // If we don't have any data yet (either no DB data or not using forceUseDb and data not fresh), fetch from Fetcher service
    if (!responseData) {
      logInfo('fetchTokenHolders', `Fetching holder data from Fetcher service for ${address} (forceUseDb: ${forceUseDb})`);
      // Add button=true parameter if this is a button interaction for special handling in the Fetcher service
      const response = await axios.get(`http://${FETCHER_HOST}:${FETCHER_PORT}/api/tokens/${address}/balances${forceUseDb ? '?button=true' : ''}`);


      if (response.data && response.data.error) {
        console.error('Error in token holders response:', response.data.error);
        return undefined;
      }

      responseData = response.data as Record<string, string>;
    }

    // Fetch pools for this token to identify which accounts are pools
    logInfo('fetchTokenHolders', `Fetching pools for token ${address}`);
    const pools = await getPoolsForToken(address);

    // If the API doesn't support pagination yet, handle it client-side
    const holderEntries = Object.entries(responseData || {});
    const totalHolders = holderEntries.length;
    const totalPages = Math.ceil(totalHolders / limit);

    // Calculate start and end indices for the current page
    const startIdx = (page - 1) * limit;
    const endIdx = Math.min(startIdx + limit, totalHolders);
    const pageHolders = holderEntries.slice(startIdx, endIdx);

    // Format holders with percentages and decimal-adjusted balances
    const holders = pageHolders.map(([accountId, balance]: [string, string]) => {
      const bigBalance = BigInt(balance);
      const totalSupplyBigInt = BigInt(tokenInfo.totalSupply);
      const percentage = totalSupplyBigInt > 0n ?
        Number((bigBalance * 10000n) / totalSupplyBigInt) / 100 : 0;

      // Calculate decimal-adjusted balance
      const divisor = 10n ** BigInt(tokenInfo.decimals || 8);
      let formattedBalance = "0";

      if (divisor > 0n) {
        const wholePart = bigBalance / divisor;
        const fractionalPart = bigBalance % divisor;

        // Format with proper decimal places
        if (fractionalPart === 0n) {
          formattedBalance = wholePart.toString();
        } else {
          const fractionalStr = fractionalPart.toString().padStart(tokenInfo.decimals, '0');
          formattedBalance = `${wholePart}.${fractionalStr}`;

          // Trim trailing zeros
          formattedBalance = formattedBalance.replace(/\.?0+$/, '');
        }
      }

      // Check if this account is a pool
      const matchingPool = pools.find(pool => pool.canisterId === accountId);

      return {
        accountId,
        balance,
        formattedBalance,
        percentage: percentage.toFixed(2),
        isPool: !!matchingPool,
        poolName: matchingPool?.pairName
      };
    });

    return {
      holders,
      pagination: {
        currentPage: page,
        totalPages,
        totalHolders,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      },
      token: {
        address: tokenInfo.address,
        name: tokenInfo.name,
        symbol: tokenInfo.symbol,
        decimals: tokenInfo.decimals,
        totalSupply: tokenInfo.totalSupply
      }
    };
  } catch (error) {
    console.error('Error fetching token holders:', error);
    throw error;
  }
};

export const fetchTokenPrice = async (address: string): Promise<{ address: string; price_usd: number } | undefined> => {
  try {
    const response = await axios.get(`http://${FETCHER_HOST}:${FETCHER_PORT}/api/tokens/${address}/price`);
    if (response.data && response.data.error) {
      console.error('Error in token price response:', response.data.error);
      return undefined;
    }
    return response.data;
  } catch (error) {
    console.error('Error fetching token price:', error);
    return undefined;
  }
};
