import WebSocket from 'ws';
import TelegramBot from 'node-telegram-bot-api';
import { BuyMessageData, NewPoolMessageData, RobertChoiceMessageData } from '../types';
import { getGroupConfigByField, getTrending } from '../db/queries';
import { sendBuyMessage, sendNewPoolMessage, sendRobertChoiceMessage } from '../libs/utils';
import { formatBuyMessage, formatNewPoolMessage, formatRobertChoiceMessage } from '../libs/messages';
import { logError, logInfo } from '../libs/logger';

export class WebSocketClient {
    private ws: WebSocket;
    private bot: TelegramBot;
    private reconnectTimeout: NodeJS.Timeout | null = null;
    private readonly reconnectInterval = 5000; // 5 seconds
    private isConnecting: boolean = false;

    constructor(bot: TelegramBot) {
        this.bot = bot;
        logInfo('WebSocket', 'Initializing WebSocket client');
        this.ws = this.connect();
    }

    private connect(): WebSocket {
        if (this.isConnecting) {
            logInfo('WebSocket', 'Connection attempt already in progress');
            return this.ws;
        }

        this.isConnecting = true;
        const wsUrl = `ws://${process.env.FETCHER_HOST || 'localhost'}:2137`;
        logInfo('WebSocket', `Connecting to ${wsUrl}`);

        const ws = new WebSocket(wsUrl);

        ws.on('open', () => {
            logInfo('WebSocket', 'Connected to Fetcher');
            this.isConnecting = false;
            if (this.reconnectTimeout) {
                clearTimeout(this.reconnectTimeout);
                this.reconnectTimeout = null;
            }
        });

        ws.on('ping', () => {
            logInfo('WebSocket', 'Received ping from server');
            ws.pong();
        });

        ws.on('message', async (data) => {
            try {
                logInfo('WebSocket', 'Received message from Fetcher');
                const rawMessage = data.toString();
                const parsedMessage = JSON.parse(rawMessage);

                // Create safe version for logging (without image data)
                const safeMessage = { ...parsedMessage };
                if (safeMessage.image) {
                    safeMessage.image = `[IMAGE_DATA_${safeMessage.image.length}_CHARS]`;
                }
                console.log('Received message:', JSON.stringify(safeMessage));
                if (parsedMessage.type === 'NewBuy') {
                    const buyMessage = parsedMessage as BuyMessageData;
                    // Get all group configurations that have this token
                    const groupsToSend = await getGroupConfigByField('address', buyMessage.gotToken.address);
                    // Send to trending first
                    const spentAmountDollars = buyMessage.spentToken.amount * buyMessage.spentToken.priceUSD;
                    logInfo('Buy Message', 'Processing buy message', {
                        token: buyMessage.gotToken.name,
                        groups: groupsToSend.length,
                        amount: buyMessage.spentToken.amount,
                        spentAmountDollars: spentAmountDollars
                    });
                    const trending = await getTrending();
                    const trendingToken = trending.slice(0, 5).find(token => token.address === buyMessage.gotToken.address);
                    if (trendingToken && spentAmountDollars > 10) {
                        try {
                            var rank = trendingToken.place;
                            const message = formatBuyMessage(buyMessage, { emoji: '🚀', socials: { website: null, openChat: null, x: null, telegram: null } }, rank);
                            await sendBuyMessage(this.bot, Number(process.env.TRENDING_CHANNEL_ID), message, `https://app.icpswap.com/swap/pro?input=${buyMessage.spentToken.address}&output=${buyMessage.gotToken.address}`);
                        } catch (error) {
                            logError('Failed to send buy message to trending', error as Error);
                        }
                    }
                    // Send message to each configured group
                    for (const config of groupsToSend) {
                        try {
                            // TODO skip transactions from lp that are not in group config.
                            // Skip if notifications are disabled or amount is below minimum
                            if (!config.active || (config.min_amount > 0 && config.min_amount > spentAmountDollars)) {
                                logInfo('Buy Message', 'Skipping group', {
                                    groupId: config.group_id,
                                    reason: !config.active ? 'inactive' : 'below minimum amount',
                                    minAmount: config.min_amount,
                                    actualAmount: buyMessage.spentToken.amount
                                });
                                continue;
                            }
                            let message = formatBuyMessage(buyMessage, { emoji: config.emoji, socials: config.socials }, rank);
                            logInfo('Buy Message', 'Sending buy message', { message });
                            // TODO make it dynamic to every dex has separated name in url
                            let buyUrl = `https://app.icpswap.com/swap/pro?input=${buyMessage.spentToken.address}&output=${buyMessage.gotToken.address}`;
                            if (parsedMessage.dex === "KongSwap") {
                                try {
                                    buyUrl = `https://www.kongswap.io/swap?from=${parsedMessage.token1.address}&to=${parsedMessage.token0.address}`
                                } catch {
                                    buyUrl = `https://www.kongswap.io/swap?from=${buyMessage.spentToken.address}&to=${buyMessage.gotToken.address}`
                                }
                            }
                            if (parsedMessage.dex === "bob.fun") {
                                buyUrl = `https://launch.bob.fun/coin/?id=${buyMessage.gotToken.address}`
                            }

                            await sendBuyMessage(this.bot, config.group_id, message, buyUrl, config.media);
                        } catch (error) {
                            logError('Failed to send buy message to group', error as Error);
                        }
                    }
                }
                if (parsedMessage.type === 'NewPool') {
                    console.log('New pool message received', parsedMessage);
                    const formattedNewPoolMessage = formatNewPoolMessage(parsedMessage)
                    // todo determine native for proper swap direction
                    let buyLink = `https://app.icpswap.com/swap/pro?input=${parsedMessage.token1.address}&output=${parsedMessage.token0.address}`

                    if (parsedMessage.dex === "KongSwap") {
                        buyLink = `https://www.kongswap.io/swap?from=${parsedMessage.token1.address}&to=${parsedMessage.token0.address}`
                    }
                    await sendNewPoolMessage(this.bot, formattedNewPoolMessage, buyLink);
                }
                if (parsedMessage.type === 'robert_choice') {
                    console.log('Robert choice message received', parsedMessage.tokenInfo);
                    const robertChoiceData = parsedMessage as RobertChoiceMessageData;
                    await sendRobertChoiceMessage(this.bot, robertChoiceData);
                }
            } catch (error) {
                logError('Error processing message', error as Error);
            }
        });

        ws.on('close', (code, reason) => {
            logInfo('WebSocket', `Connection closed. Code: ${code}, Reason: ${reason.toString()}`);
            this.isConnecting = false;
            this.scheduleReconnect();
        });

        ws.on('error', (error) => {
            logError('WebSocket error', error);
            this.isConnecting = false;
            ws.close();
        });

        return ws;
    }

    private scheduleReconnect() {
        if (!this.reconnectTimeout && !this.isConnecting) {
            logInfo('WebSocket', `Scheduling reconnect in ${this.reconnectInterval}ms`);
            this.reconnectTimeout = setTimeout(() => {
                this.ws = this.connect();
            }, this.reconnectInterval);
        }
    }

    public close() {
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
        }
        logInfo('WebSocket', 'Closing WebSocket client');
        this.ws.close();
    }
}
