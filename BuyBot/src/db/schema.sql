-- Function to automatically update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Pools table for storing ICP Swap pool information
CREATE TABLE pools (
    -- Primary key using the unique key from the pool data
    key VA<PERSON>HAR(255) PRIMARY KEY,
    
    -- Pool basic info
    fee BIGINT NOT NULL,
    tick_spacing BIGINT NOT NULL,
    canister_id VARCHAR(63) NOT NULL,
    
    -- Token0 info
    token0_address VARCHAR(63) NOT NULL,
    token0_standard VARCHAR(10) NOT NULL,
    
    -- Token1 info
    token1_address VARCHAR(63) NOT NULL,
    token1_standard VARCHAR(10) NOT NULL,
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for pools table
CREATE INDEX idx_pools_canister_id ON pools(canister_id);
CREATE INDEX idx_pools_token0_address ON pools(token0_address);
CREATE INDEX idx_pools_token1_address ON pools(token1_address);


-- Trigger to update updated_at on pools table
CREATE TRIGGER update_pools_updated_at
    BEFORE UPDATE ON pools
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Tokens table for storing token information
CREATE TABLE tokens (
    address VARCHAR(100) NOT NULL PRIMARY KEY,
    decimals INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    total_supply NUMERIC,
    standard VARCHAR(50),
    owner VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Trigger to update updated_at on tokens table
CREATE TRIGGER update_tokens_updated_at
    BEFORE UPDATE ON tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();


-- Users table for telegram bot users
CREATE TABLE users (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    currently_setting BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Trigger to update updated_at on users table
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Groups table for telegram groups
CREATE TABLE groups (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100),
    link VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Trigger to update updated_at on groups table
CREATE TRIGGER update_groups_updated_at
    BEFORE UPDATE ON groups
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Group configurations table
CREATE TABLE group_configs (
    id SERIAL PRIMARY KEY,
    group_id BIGINT NOT NULL UNIQUE,
    address VARCHAR(100),
    media BYTEA,
    emoji VARCHAR(10),
    min_amount INT,
    pools VARCHAR(100)[],
    socials JSONB,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES groups (id),
    FOREIGN KEY (address) REFERENCES tokens (address)
);

-- Trigger to update updated_at on group_configs table
CREATE TRIGGER update_group_configs_updated_at
    BEFORE UPDATE ON group_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE user_groups (
    user_id BIGINT NOT NULL,
    group_id BIGINT NOT NULL,
    role VARCHAR(50) DEFAULT 'admin',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, group_id),
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (group_id) REFERENCES groups (id)
);

-- Trigger to update updated_at on user_groups table
CREATE TRIGGER update_user_groups_updated_at
    BEFORE UPDATE ON user_groups
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();


CREATE TABLE token_metrics (
    address VARCHAR(100) NOT NULL PRIMARY KEY,
    volume_usd_1d NUMERIC,
    volume_usd_7d NUMERIC,
    total_volume_usd NUMERIC,
    volume_usd NUMERIC,
    fees_usd NUMERIC,
    price_usd_change NUMERIC,
    tx_count INTEGER,
    price_usd NUMERIC,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (address) REFERENCES tokens (address) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TRIGGER update_token_metrics_updated_at
    BEFORE UPDATE ON token_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TABLE trending (
    address VARCHAR(100) NOT NULL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    volume_usd_1d NUMERIC,
    price_usd NUMERIC,
    price_usd_change NUMERIC,
    socials JSONB,
    place INTEGER,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER update_trending_updated_at
    BEFORE UPDATE ON trending
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- HOLDERS CHANGES

-- Add fields to tokens table for tracking fetch status
ALTER TABLE tokens 
ADD COLUMN last_fetch_index TEXT,
ADD COLUMN last_fetch_time TIMESTAMP WITH TIME ZONE;

-- Create table for token holder accounts
CREATE TABLE accounts (
    id SERIAL PRIMARY KEY,
    account_id VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster account lookups
CREATE INDEX idx_accounts_account_id ON accounts(account_id);

-- Create table for token holder balances
CREATE TABLE token_balances (
    token_address VARCHAR(100) NOT NULL,
    account_id INTEGER NOT NULL,
    balance NUMERIC NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (token_address, account_id),
    FOREIGN KEY (token_address) REFERENCES tokens(address) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE
);

-- Create index for balance sorting
CREATE INDEX idx_token_balances_balance ON token_balances(balance DESC);

-- Create trigger to update last_updated column
CREATE TRIGGER update_token_balances_last_updated
    BEFORE UPDATE ON token_balances
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Optional: Create a view to simplify querying top holders
CREATE VIEW token_top_holders AS
SELECT 
    t.address AS token_address,
    t.symbol AS token_symbol,
    a.account_id, 
    tb.balance,
    tb.last_updated
FROM 
    token_balances tb
JOIN 
    tokens t ON tb.token_address = t.address
JOIN 
    accounts a ON tb.account_id = a.id
ORDER BY 
    tb.token_address, tb.balance DESC;


-- Drop the existing foreign key constraint
ALTER TABLE token_metrics DROP CONSTRAINT token_metrics_address_fkey;

-- Add the new foreign key constraint with CASCADE
ALTER TABLE token_metrics ADD CONSTRAINT token_metrics_address_fkey
    FOREIGN KEY (address) REFERENCES tokens (address) ON DELETE CASCADE ON UPDATE CASCADE;

-- Robert's Choice tracking table
CREATE TABLE robert_choice (
    id SERIAL PRIMARY KEY,
    address VARCHAR(10) NOT NULL,
    ticker VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at_token TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    zero_point_one_icp_tokens_out BIGINT,
    image TEXT,
    is_current BOOLEAN NOT NULL DEFAULT FALSE,
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index for quick lookup of current choice
CREATE INDEX idx_robert_choice_current ON robert_choice (is_current) WHERE is_current = TRUE;

-- Trigger to update updated_at
CREATE TRIGGER update_robert_choice_updated_at
    BEFORE UPDATE ON robert_choice
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- OpenChat Bot Registration Tables
-- ================================

-- OpenChat groups table for storing group information
CREATE TABLE openchat_groups (
    group_id VARCHAR(100) PRIMARY KEY,
    group_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    added_by VARCHAR(100) NOT NULL, -- User principal who first added the bot
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index for active groups lookup
CREATE INDEX idx_openchat_groups_active ON openchat_groups(is_active);
CREATE INDEX idx_openchat_groups_last_activity ON openchat_groups(last_activity);

-- Trigger to update updated_at on openchat_groups table
CREATE TRIGGER update_openchat_groups_updated_at
    BEFORE UPDATE ON openchat_groups
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- OpenChat group admins table for storing admin permissions
CREATE TABLE openchat_group_admins (
    group_id VARCHAR(100) NOT NULL,
    user_principal VARCHAR(100) NOT NULL,
    added_by VARCHAR(100) NOT NULL,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    PRIMARY KEY (group_id, user_principal),
    FOREIGN KEY (group_id) REFERENCES openchat_groups(group_id) ON DELETE CASCADE
);

-- Index for admin lookups
CREATE INDEX idx_openchat_group_admins_user ON openchat_group_admins(user_principal);
CREATE INDEX idx_openchat_group_admins_active ON openchat_group_admins(is_active);

-- OpenChat watched tokens table for storing token watch configurations
CREATE TABLE openchat_watched_tokens (
    id SERIAL PRIMARY KEY,
    group_id VARCHAR(100) NOT NULL,
    token_address VARCHAR(100) NOT NULL,
    token_symbol VARCHAR(20),
    token_name VARCHAR(255),
    min_amount NUMERIC DEFAULT 0, -- Minimum USD amount to trigger alerts
    added_by VARCHAR(100) NOT NULL, -- User principal who added this token
    added_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(group_id, token_address),
    FOREIGN KEY (group_id) REFERENCES openchat_groups(group_id) ON DELETE CASCADE,
    FOREIGN KEY (token_address) REFERENCES tokens(address) ON DELETE CASCADE ON UPDATE CASCADE
);

-- Indexes for efficient token lookups
CREATE INDEX idx_openchat_watched_tokens_group ON openchat_watched_tokens(group_id);
CREATE INDEX idx_openchat_watched_tokens_token ON openchat_watched_tokens(token_address);
CREATE INDEX idx_openchat_watched_tokens_active ON openchat_watched_tokens(is_active);
CREATE INDEX idx_openchat_watched_tokens_min_amount ON openchat_watched_tokens(min_amount);

-- Trigger to update updated_at on openchat_watched_tokens table
CREATE TRIGGER update_openchat_watched_tokens_updated_at
    BEFORE UPDATE ON openchat_watched_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- OpenChat bot activity log for tracking sent messages and errors
CREATE TABLE openchat_bot_activity (
    id SERIAL PRIMARY KEY,
    group_id VARCHAR(100) NOT NULL,
    token_address VARCHAR(100),
    activity_type VARCHAR(50) NOT NULL, -- 'message_sent', 'message_failed', 'token_registered', 'token_unregistered', 'admin_added', 'admin_removed'
    message_content TEXT,
    user_principal VARCHAR(100),
    trade_amount NUMERIC, -- USD amount for buy alerts
    error_code INTEGER, -- OpenChat error code if applicable
    error_message TEXT,
    success BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES openchat_groups(group_id) ON DELETE CASCADE,
    FOREIGN KEY (token_address) REFERENCES tokens(address) ON DELETE SET NULL ON UPDATE CASCADE
);

-- Indexes for activity log
CREATE INDEX idx_openchat_bot_activity_group ON openchat_bot_activity(group_id);
CREATE INDEX idx_openchat_bot_activity_type ON openchat_bot_activity(activity_type);
CREATE INDEX idx_openchat_bot_activity_created_at ON openchat_bot_activity(created_at);
CREATE INDEX idx_openchat_bot_activity_success ON openchat_bot_activity(success);
CREATE INDEX idx_openchat_bot_activity_token ON openchat_bot_activity(token_address);

-- View for OpenChat group statistics
CREATE VIEW openchat_group_stats AS
SELECT
    og.group_id,
    og.group_name,
    og.is_active,
    og.created_at,
    og.last_activity,
    COUNT(DISTINCT owt.token_address) as watched_tokens_count,
    COUNT(DISTINCT oga.user_principal) as admin_count,
    COUNT(CASE WHEN oba.activity_type = 'message_sent' AND oba.success = true THEN 1 END) as successful_messages,
    COUNT(CASE WHEN oba.activity_type = 'message_sent' AND oba.success = false THEN 1 END) as failed_messages,
    MAX(oba.created_at) as last_message_sent
FROM
    openchat_groups og
LEFT JOIN
    openchat_watched_tokens owt ON og.group_id = owt.group_id AND owt.is_active = true
LEFT JOIN
    openchat_group_admins oga ON og.group_id = oga.group_id AND oga.is_active = true
LEFT JOIN
    openchat_bot_activity oba ON og.group_id = oba.group_id
GROUP BY
    og.group_id, og.group_name, og.is_active, og.created_at, og.last_activity;

-- View for most watched tokens across all OpenChat groups
CREATE VIEW openchat_popular_tokens AS
SELECT
    t.address,
    t.symbol,
    t.name,
    COUNT(DISTINCT owt.group_id) as watching_groups_count,
    AVG(owt.min_amount) as avg_min_amount,
    MIN(owt.min_amount) as min_min_amount,
    MAX(owt.min_amount) as max_min_amount,
    COUNT(CASE WHEN oba.activity_type = 'message_sent' AND oba.success = true THEN 1 END) as total_alerts_sent,
    SUM(oba.trade_amount) as total_trade_volume_alerted,
    MAX(owt.added_at) as last_added_at
FROM
    tokens t
JOIN
    openchat_watched_tokens owt ON t.address = owt.token_address AND owt.is_active = true
LEFT JOIN
    openchat_bot_activity oba ON t.address = oba.token_address
GROUP BY
    t.address, t.symbol, t.name
ORDER BY
    watching_groups_count DESC, total_alerts_sent DESC;