import { Pool } from "pg";
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Try multiple potential .env file locations
const potentialPaths = [
    path.resolve(process.cwd(), '.env'),
    path.resolve(__dirname, '../../.env'),
    path.resolve(__dirname, '../../../.env'),
    path.resolve(__dirname, '../../../../.env')
];

let envLoaded = false;
for (const envPath of potentialPaths) {
    if (fs.existsSync(envPath)) {
        console.log(`Found .env file at: ${envPath}`);
        dotenv.config({ path: envPath });
        envLoaded = true;
        break;
    } else {
        console.log(`No .env file found at: ${envPath}`);
    }
}

if (!envLoaded) {
    console.warn('No .env file found in any of the expected locations!');
}// Create a connection pool with improved reliability settings
export const postgres = new Pool({
    user: process.env.PGUSER || "postgres",
    host: process.env.PGHOST || "localhost",
    database: process.env.PGDATABASE || "postgres",
    password: process.env.PGPASSWORD || "password",
    port: Number(process.env.PGPORT) || 5432,
    connectionTimeoutMillis: 60000,  // 60 second timeout for connection attempts
    idleTimeoutMillis: 120000,       // 2 minutes idle timeout
    max: 30,                         // Maximum 30 connections in the pool
    min: 3,                          // Keep at least 3 connections ready
    allowExitOnIdle: false,          // Don't exit on idle
    keepAlive: true,                 // Keep connections alive
    keepAliveInitialDelayMillis: 5000, // 5 seconds before first keepalive
});

// Log connection events
postgres.on('connect', (client) => {
    console.log('New database connection established');
});

postgres.on('error', (err, client) => {
    console.error('Unexpected error on idle client', err);
});

postgres.on('remove', (client) => {
    console.log('Database connection removed from pool');
});