import { postgres } from "./dbConnector";
import { Group } from "../types";
interface PoolInfo {
    canisterId: string;
    pairName: string;
}

export const saveUser = async (userId: bigint, username: string) => {
    try {
        await postgres.query('INSERT INTO users (id, username) VALUES ($1, $2) ON CONFLICT (id) DO NOTHING', [userId, username]);
    }
    catch (err) {
        console.error('Error saving user:', err);
    }

}

export const updateUserCol = async (userId: bigint, col: string, value: any): Promise<void> => {
    try {
        const query = `UPDATE users SET ${col} = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2`;
        await postgres.query(query, [value, userId]);
    } catch (err) {
        console.error('Error updating user:', err);
    }
}

export const upsertGroup = async (groupId: bigint, groupTitle: string, link: string): Promise<void> => {
    try {
        const query = `INSERT INTO groups (id, name, link) VALUES ($1, $2, $3) ON CONFLICT (id) DO UPDATE SET name = $2, link = $3, updated_at = CURRENT_TIMESTAMP`;
        await postgres.query(query, [groupId, groupTitle, link]);
    } catch (err) {
        console.error('Error upserting group:', err);
    }
}

export const upsertGroupUser = async (groupId: bigint, userId: bigint): Promise<void> => {
    try {
        const query = `INSERT INTO user_groups (group_id, user_id) VALUES ($1, $2) ON CONFLICT (group_id, user_id) DO NOTHING`;
        await postgres.query(query, [groupId, userId]);
    } catch (err) {
        console.error('Error upserting group user:', err);
    }
}

export const getAllConfigsForUser = async (userId: bigint) => {
    try {
        const query = 'SELECT * FROM group_configs WHERE group_id IN (SELECT group_id FROM user_groups WHERE user_id = $1)';
        const result = await postgres.query(query, [userId]);
        return result.rows;
    } catch (err) {
        console.error('Error getting all configs for user:', err);
        return [];
    }
}

export const getConfigsForUserView = async (userId: bigint) => {
    try {
        const query = 'SELECT group_configs.group_id as "configGroupId", COALESCE((SELECT name from tokens WHERE address = group_configs.address), group_configs.address) as "tokenName", COALESCE((SELECT name from groups WHERE id = group_configs.group_id), \'PRIVATE\') as "groupName" FROM group_configs WHERE group_id IN (SELECT group_id FROM user_groups WHERE user_id = $1)';
        const result = await postgres.query(query, [userId]);
        return result.rows;
    } catch (err) {
        console.error('Error getting all configs for user:', err);
        return [];
    }
}

export const handleGroupTypeChange = async (oldGroupId: bigint, group: Group): Promise<void> => {
    try {
        const query = `
            WITH old_user AS (
                SELECT user_id FROM user_groups WHERE group_id = $1
            ), upsert_group AS (
                INSERT INTO groups (id, name, link)
                VALUES ($2, $3, $4)
                ON CONFLICT (id) DO UPDATE
                SET name = EXCLUDED.name, link = EXCLUDED.link, updated_at = CURRENT_TIMESTAMP
            ), move_user AS (
                INSERT INTO user_groups (group_id, user_id)
                SELECT $2, user_id FROM old_user
                ON CONFLICT (group_id, user_id) DO NOTHING
            )
            DELETE FROM user_groups WHERE group_id = $1 AND user_id IN (SELECT user_id FROM old_user)
        `;

        await postgres.query(query, [oldGroupId, group.id, group.name, group.link]);
    } catch (err) {
        console.error('Error handling group type change:', err);
        throw err;
    }
}

export const upsertToken = async (token: {
    address: string;
    decimals: number;
    name: string;
    symbol: string;
    totalSupply: string;
    standard: string;
    owner: string;
}): Promise<void> => {
    try {
        const query = `
    INSERT INTO tokens (
        address,
        decimals,
        name,
        symbol,
        total_supply,
        standard,
        owner
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7)
    ON CONFLICT (address)
    DO UPDATE SET
        total_supply = EXCLUDED.total_supply,
        updated_at = CURRENT_TIMESTAMP
`;

        const values = [
            token.address,
            token.decimals,
            token.name,
            token.symbol,
            token.totalSupply,
            token.standard,
            token.owner,
        ];

        await postgres.query(query, values);
    } catch (error) {
        console.error('Error in upsertToken:', error);
    }
}

export const getGroupConfig = async (groupId: bigint) => {
    try {
        const query = 'SELECT * FROM group_configs WHERE group_id = $1';
        const result = await postgres.query(query, [groupId]);
        return result.rows[0];
    } catch (err) {
        console.error('Error getting group config:', err);
    }
}
export const getGroupData = async (groupId: bigint) => {
    try {
        const query = 'SELECT * FROM groups WHERE id = $1';
        const result = await postgres.query(query, [groupId]);
        return result.rows[0];
    } catch (err) {
        console.error('Error getting group config:', err);
    }
}
export const getGroupConfigByField = async (field: string, value: any) => {
    try {
        const query = `SELECT * FROM group_configs WHERE ${field} = $1`;
        const result = await postgres.query(query, [value]);
        return result.rows;
    } catch (err) {
        console.error(`Error getting group config by field ${field}:`, err);
        return [];
    }
}

export const insertDefaultGroupConfig = async (groupId: bigint, address: string): Promise<void> => {
    try {
        const pools = await getPoolsForToken(address);
        const defaultSocials = JSON.stringify({
            website: null,
            telegram: null,
            x: null,
            openChat: null
        });

        // First check if a config already exists
        const existingConfig = await getGroupConfig(groupId);
        if (existingConfig) {
            console.log('Group config already exists');
        } else {
            // If doesn't exist, insert new
            const query = `
                INSERT INTO group_configs (
                    group_id, 
                    address,
                    pools, 
                    socials,
                    emoji,
                    min_amount
                ) VALUES ($1, $2, $3, $4, '🚀', 0)
            `;
            const poolsJson = pools.map(p => JSON.stringify(p));
            await postgres.query(query, [groupId, address, poolsJson, defaultSocials]);
        }
    } catch (err) {
        console.error('Error inserting/updating default group config:', err);
        throw err;
    }
}

export const updateGroupConfig = async (groupId: bigint, field: string, value: any): Promise<void> => {
    try {
        // First ensure group_config exists
        const existingConfig = await getGroupConfig(groupId);
        if (!existingConfig) {
            throw new Error('No group config found for this group');
        }

        let query;
        if (field === 'pools') {
            query = `UPDATE group_configs SET ${field} = $1::varchar[], updated_at = CURRENT_TIMESTAMP WHERE group_id = $2`;
        } else if (['website', 'telegram', 'x', 'openChat'].includes(field)) {
            // Handle social media updates by updating the JSONB field
            query = `
                UPDATE group_configs 
                SET socials = jsonb_set(
                    COALESCE(socials, '{}'::jsonb),
                    '{${field}}',
                    $1::jsonb,
                    true
                ),
                updated_at = CURRENT_TIMESTAMP 
                WHERE group_id = $2
            `;
            // Convert the value to a JSON string or null
            value = value ? JSON.stringify(value) : 'null';
        } else {
            query = `UPDATE group_configs SET ${field} = $1, updated_at = CURRENT_TIMESTAMP WHERE group_id = $2`;
        }
        console.log('Updating group config:', { groupId, field, value, query });
        await postgres.query(query, [value, groupId]);
    } catch (err) {
        console.error('Error updating group config:', err);
        throw err;
    }
}

export const getCurrentGroupId = async (userId: number): Promise<bigint | null> => {
    try {
        const result = await postgres.query(
            'SELECT currently_setting FROM users WHERE id = $1',
            [userId]
        );
        return result.rows[0]?.currently_setting || null;
    } catch (err) {
        console.error('Error getting current group ID:', err);
        return null;
    }
};

export const getPoolsForToken = async (tokenAddress: string): Promise<PoolInfo[]> => {
    try {
        const query = `
        SELECT 
            p.canister_id,
            CASE 
                WHEN p.canister_id LIKE 'KONG%' THEN 
                    CASE 
                        WHEN p.token0_address = $1 THEN CONCAT('KONG ', t1.symbol, '/', t2.symbol)
                        ELSE CONCAT('KONG ', t2.symbol, '/', t1.symbol)
                    END
                ELSE 
                    CASE 
                        WHEN p.token0_address = $1 THEN CONCAT(t1.symbol, '/', t2.symbol)
                        ELSE CONCAT(t2.symbol, '/', t1.symbol)
                    END
            END as pair_name
        FROM pools p
        JOIN tokens t1 ON p.token0_address = t1.address
        JOIN tokens t2 ON p.token1_address = t2.address
        WHERE p.token0_address = $1 OR p.token1_address = $1
    `;

        const result = await postgres.query(query, [tokenAddress]);
        return result.rows.map((row: any) => ({
            canisterId: row.canister_id,
            pairName: row.pair_name
        }));
    } catch (err) {
        console.error('Error getting pools for token:', err);
        return [];
    }
}

export const getAllPools = async (tokenAddress: string): Promise<string[]> => {
    try {
        const query = `
            SELECT DISTINCT canister_id
            FROM pools
            WHERE token0_address = $1 OR token1_address = $1
        `;
        const result = await postgres.query(query, [tokenAddress]);
        return result.rows.map((row: any) => row.canister_id);
    } catch (err) {
        console.error('Error getting all pools:', err);
        return [];
    }
}

export const updateGroupConfigPools = async (groupId: bigint, pools: string[] | 'all'): Promise<void> => {
    try {
        let query;
        let values;

        if (pools === 'all') {
            // Get the token address for this group
            const groupConfig = await getGroupConfig(groupId);
            if (!groupConfig?.address) {
                throw new Error('No token address configured for this group');
            }

            // Get all pools for the token
            const allPools = await getAllPools(groupConfig.address);
            query = `UPDATE group_configs SET pools = $1::varchar[], updated_at = CURRENT_TIMESTAMP WHERE group_id = $2`;
            values = [allPools, groupId];
        } else {
            query = `UPDATE group_configs SET pools = $1::varchar[], updated_at = CURRENT_TIMESTAMP WHERE group_id = $2`;
            values = [pools, groupId];
        }

        await postgres.query(query, values);
    } catch (err) {
        console.error('Error updating group config pools:', err);
        throw err;
    }
}

export const getTokenInfoFromDB = async (address: string): Promise<{
    address: string;
    decimals: number;
    name: string;
    symbol: string;
    totalSupply: string;
    standard: string;
    owner: string;
    updatedAt: Date;
} | null> => {
    try {
        const query = `
            SELECT address, decimals, name, symbol, total_supply as "totalSupply", 
                   standard, owner, updated_at as "updatedAt"
            FROM tokens 
            WHERE address = $1`;
        const result = await postgres.query(query, [address]);
        return result.rows[0] || null;
    } catch (err) {
        console.error('Error getting token info from DB:', err);
        return null;
    }
}

export const getTrending = async (): Promise<any[]> => {
    try {
        const query = `SELECT * from trending`;
        const result = await postgres.query(query);
        return result.rows;
    } catch (err) {
        console.error('Error getting trending:', err);
        return [];
    }
}

/**
 * Check if a token's holder data in the database is fresh enough
 * @param tokenAddress The token's canister ID
 * @param maxAgeMinutes Maximum age of the data in minutes to be considered fresh
 * @returns Object with freshness info or null if token not found
 */
export const checkTokenHoldersFreshness = async (tokenAddress: string, maxAgeMinutes: number = 5): Promise<{ isFresh: boolean, lastFetchTime: Date | null, lastFetchIndex: string | null }> => {
    try {
        // Check if token exists and when it was last updated
        const query = 'SELECT last_fetch_time, last_fetch_index FROM tokens WHERE address = $1';
        const result = await postgres.query(query, [tokenAddress]);

        if (result.rows.length === 0) {
            return { isFresh: false, lastFetchTime: null, lastFetchIndex: null };
        }

        const lastFetchTime = result.rows[0].last_fetch_time;
        const lastFetchIndex = result.rows[0].last_fetch_index;

        // Check if data is fresh enough
        const maxAgeMs = maxAgeMinutes * 60 * 1000; // Convert to milliseconds
        const dataAgeMs = Date.now() - new Date(lastFetchTime).getTime();
        const isFresh = dataAgeMs < maxAgeMs;

        return {
            isFresh,
            lastFetchTime,
            lastFetchIndex
        };
    } catch (error) {
        console.error('Error checking token holders freshness:', error);
        return { isFresh: false, lastFetchTime: null, lastFetchIndex: null };
    }
}

/**
 * Get token holder balances directly from the database
 * @param tokenAddress The token's canister ID
 * @returns Object mapping account IDs to balance strings, or undefined if not found/error
 */
export const getTokenHoldersFromDB = async (tokenAddress: string): Promise<Record<string, string> | undefined> => {
    try {
        // Check if the token exists - no need to get an ID since we use address directly
        const tokenQuery = 'SELECT address FROM tokens WHERE address = $1';
        const tokenResult = await postgres.query(tokenQuery, [tokenAddress]);

        if (tokenResult.rows.length === 0) {
            console.log(`Token ${tokenAddress} not found in database`);
            return undefined;
        }

        // Get all balances for this token - note the schema uses token_address, not token_id
        // Important: Sort by balance in descending order to match the Fetcher service behavior
        const balancesQuery = `
            SELECT a.account_id, tb.balance 
            FROM token_balances tb
            JOIN accounts a ON tb.account_id = a.id
            WHERE tb.token_address = $1
            ORDER BY tb.balance DESC
        `;

        const balancesResult = await postgres.query(balancesQuery, [tokenAddress]);

        if (balancesResult.rows.length === 0) {
            console.log(`No balances found for token ${tokenAddress}`);
            return undefined;
        }

        // Convert to the expected format
        const balances: Record<string, string> = {};
        for (const row of balancesResult.rows) {
            balances[row.account_id] = row.balance;
        }

        return balances;
    } catch (error) {
        console.error('Error getting token holders from DB:', error);
        return undefined;
    }
}

export const getTokenMetricsFromDB = async (address: string): Promise<any | null> => {
    try {
        const query = `
            SELECT
                address,
                volume_usd_1d,
                volume_usd_7d,
                total_volume_usd,
                volume_usd,
                fees_usd,
                price_usd_change,
                tx_count,
                price_usd,
                updated_at
            FROM
                token_metrics
            WHERE
                address = $1
        `;
        const result = await postgres.query(query, [address]);
        return result.rows[0] || null;
    } catch (error) {
        console.error('Error getting token metrics from DB:', error);
        return null;
    }
}
