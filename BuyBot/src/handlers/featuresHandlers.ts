import { logError, logInfo, logDebug } from '../libs/logger';
import TelegramBot from 'node-telegram-bot-api';
import { getGroupConfig } from '../db/queries';
import { fetchTokenHolders } from '../requests/requests';



// Store user page state in memory
const userPageState: Record<number, { address: string, page: number }> = {};

export const getHoldersHandler = async (bot: TelegramBot, msg: TelegramBot.Message | TelegramBot.CallbackQuery) => {
    try {
        // Handle both message and callback query
        const isCallback = 'data' in msg;
        let chatId: number;
        let address: string;
        let page = 1;
        
        if (isCallback && msg.data) {
            // This is a callback from pagination buttons
            chatId = msg.message!.chat.id;
            const data = msg.data.split(':');
            address = data[1];
            page = parseInt(data[2], 10);
            
            // Update page state for this user
            userPageState[chatId] = { address, page };
            
            // Answer callback query to remove loading state
            await bot.answerCallbackQuery(msg.id);
        } else if ('text' in msg && msg.text && msg.text.split(' ').length > 1) {
            // Command with specified token address
            chatId = msg.chat.id;
            address = msg.text.split(' ')[1];
            page = 1;
            
            // Save initial page state
            userPageState[chatId] = { address, page };
        } else if ('text' in msg && msg.chat) {
            // Handle command without specified token - try to use group config
            chatId = msg.chat.id;
            
            const config = await getGroupConfig(BigInt(chatId));
            if (!config) {
                await bot.sendMessage(chatId, 'Group configuration not found. Please register your token first using /start in private chat with bot');
                return;
            }
            
            address = config.address;
            page = 1;
            
            // Save initial page state
            userPageState[chatId] = { address, page };
        } else {
            return; // Should never happen, but TS needs this
        }
        
        // Set page size
        const PAGE_SIZE = 20;
        
        // Show loading message
        let messageId: number;
        if (isCallback) {
            await bot.editMessageText('Loading holders data...', { 
                chat_id: chatId, 
                message_id: (msg as TelegramBot.CallbackQuery).message!.message_id 
            });
            messageId = (msg as TelegramBot.CallbackQuery).message!.message_id;
        } else {
            const loadingMessage = await bot.sendMessage(chatId, 'Loading holders data...');
            messageId = loadingMessage.message_id;
        }
        
        logInfo('getHoldersHandler', `Fetching holders for token ${address} (page ${page})`);
        
        // Different behavior based on how the handler was triggered:
        // - For initial command (/holders), try to get fresh data (or cached if fresh enough)
        // - For button navigations, always use database data
        const shouldForceUseDb = isCallback;
        
        // Log which mode we're using
        if (shouldForceUseDb) {
            logInfo('getHoldersHandler', `Button navigation - using database for ${address} page ${page}`);
        } else {
            logInfo('getHoldersHandler', `Initial command - checking for fresh data for ${address}`);
        }
        
        // Fetch holders with pagination
        const holdersData = await fetchTokenHolders(address, page, PAGE_SIZE, shouldForceUseDb);
        
        if (!holdersData || !holdersData.holders || holdersData.holders.length === 0) {
            const noDataMessage = 'No holders found for this token.';
            // Always edit the loading message instead of sending a new one
            await bot.editMessageText(noDataMessage, { 
                chat_id: chatId, 
                message_id: messageId 
            });
            return;
        }
        
        // Get token info and pagination data
        const { token, pagination } = holdersData;
        const { currentPage, totalPages, totalHolders } = pagination;
        
        // Format the holders in a readable way with truncated account IDs and links
        const formattedHolders = holdersData.holders.map((holder, index) => {
            // Format account for display
            let displayAccount = holder.accountId;
            
            // If this account is a pool, use the pool name instead
            if (holder.isPool && holder.poolName) {
                displayAccount = holder.poolName; // Use the pool name (e.g. "SNS/ICP")
            } else if (holder.accountId.length > 12) {
                // Otherwise truncate if longer than 12 chars
                displayAccount = `${holder.accountId.substring(0, 4)}...${holder.accountId.substring(holder.accountId.length - 3)}`;
            }
            
            // Create clickable link to ICExplorer - always link to the original account ID
            const accountLink = `[${displayAccount}](https://www.icexplorer.io/address/details/${holder.accountId})`;
            
            // Format balance with commas and ensure whole numbers without decimals
            const balanceWithoutDecimals = holder.formattedBalance?.includes('.') 
                ? holder.formattedBalance.substring(0, holder.formattedBalance.indexOf('.'))
                : holder.formattedBalance;
            const formattedBalance = balanceWithoutDecimals?.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            
            // Add percentage ownership
            const percentage = holder.percentage ? `(${holder.percentage}%)` : '';
            
            // Calculate holder's position in the overall list
            const position = (currentPage - 1) * PAGE_SIZE + index + 1;
            
            return `${position}. ${accountLink}: ${formattedBalance} ${percentage}`;
        }).join('\n');
        
        // Create token info header
        const tokenInfo = token ? 
            `*${token.name || 'Unknown'}* (${token.symbol || '?'})\n${token.address}\n` : 
            '';
        
        // Create message with pagination info
        const message = `📊 *Token Holder Distribution*\n\n${tokenInfo}*Page ${currentPage} of ${totalPages}*\n*Total Holders:* ${totalHolders}\n\n${formattedHolders}`;
        
        // Create pagination buttons if needed
        const inlineKeyboard: TelegramBot.InlineKeyboardButton[][] = [];
        const navigationRow: TelegramBot.InlineKeyboardButton[] = [];
        
        // Add Previous button if not on first page
        if (pagination.hasPreviousPage) {
            navigationRow.push({
                text: '⬅️ Previous',
                callback_data: `holders:${address}:${currentPage - 1}`
            });
        }
        
        // Add Next button if not on last page
        if (pagination.hasNextPage) {
            navigationRow.push({
                text: 'Next ➡️',
                callback_data: `holders:${address}:${currentPage + 1}`
            });
        }
        
        // Only add keyboard if we have navigation buttons
        if (navigationRow.length > 0) {
            inlineKeyboard.push(navigationRow);
        }
        
        const options: TelegramBot.SendMessageOptions = {
            parse_mode: 'Markdown',
            reply_markup: inlineKeyboard.length ? { inline_keyboard: inlineKeyboard } : undefined,
            disable_web_page_preview: true
        };
        
        // Always update the loading message with the holders data
        const editOptions: TelegramBot.EditMessageTextOptions = { 
            chat_id: chatId, 
            message_id: messageId,
            parse_mode: 'Markdown',
            reply_markup: inlineKeyboard.length ? { inline_keyboard: inlineKeyboard } : undefined,
            disable_web_page_preview: true
        };
        await bot.editMessageText(message, editOptions);
    } catch (error) {
        logError('getHoldersHandler', error as Error);
        
        // Determine chatId for error response
        let errorChatId: number;
        if ('data' in msg && msg.message) {
            errorChatId = msg.message.chat.id;
        } else if ('chat' in msg) {
            errorChatId = msg.chat.id;
        } else {
            // Can't determine chatId, can't send error message
            return;
        }
        
        await bot.sendMessage(errorChatId, 'An error occurred while fetching the holders. Please try again later.');
    }
};