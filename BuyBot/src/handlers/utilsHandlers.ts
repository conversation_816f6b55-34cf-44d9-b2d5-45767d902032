import { handleGroupType<PERSON>hange, getGroupConfig, getTokenMetricsFromDB } from "../db/queries";
import { fetchTokenInfo } from "../requests/requests";
import TelegramBot from 'node-telegram-bot-api';
import { logError, logInfo } from '../libs/logger';
import { fetchBobTokenInfo } from "../requests/requests";

const botSetupGuideMessage: string = `
Hello\\! Here's how to set me up for your Telegram group\\.

*Bot Setup Guide*

*Step 1: Start the Configuration*
\\- Send me the \`/start\` command here in our private chat\\.
\\- You will see a menu\\. Click the _"Register Token"_ button\\.
\\- Paste your token's *Canister ID* \\(or *Token ID* from launch\\.bob\\) and send it\\.

*Step 2: Configure Your Token*
After registering, the main configuration menu will appear\\. Here's what each option does:

🖼️ *Set Media*: Set a custom image or GIF that appears in your group's buy notifications\\.
🚀 *Emoji*: Choose a specific emoji \\(like a rocket\\) to be displayed with every notification\\.
*Min Amount*: Set the minimum purchase value \\(e\\.g\\., \\$10\\) required to trigger a buy notification\\.
*Pools*: Configure the liquidity pools the bot should track for this token\\.
*Socials*: Add your project's official social media links \\(Website, Twitter, etc\\.\\) to be shown in messages\\.
🟢 *Notifications*: Toggle buy notifications on \\(🟢\\) or off \\(🔴\\) for your group\\.
*Test*: Sends a sample notification to confirm that the setup is working correctly\\.

*After Setup*
\`/details\` \\(or \`/d\`\\): Use this command inside your configured group at any time to get the latest token metrics\\.
\`/config\`: Send this command to me in a private message anytime you want to get a menu with already configured groups\\.
`;

export const helpHandler = async (bot: TelegramBot, msg: TelegramBot.Message) => {
    const chatId = msg.chat.id;

    // Only allow help command in private messages
    if (msg.chat.type !== 'private') {
        return; // Silently ignore in groups/channels
    }

    try {
        await bot.sendMessage(chatId, botSetupGuideMessage, {
            parse_mode: 'MarkdownV2',
            disable_web_page_preview: true
        });
    } catch (error) {
        console.error('Error sending help message:', error);
        await bot.sendMessage(chatId, 'Sorry, there was an error displaying the help message. Please try again later.');
    }
};

// Rate limiting for details command: 5 uses per 5 minutes per group
interface RateLimitEntry {
    count: number;
    resetTime: number;
}

const detailsRateLimit = new Map<number, RateLimitEntry>();

const checkRateLimit = (chatId: number): { allowed: boolean; remainingUses?: number; resetTime?: number } => {
    const now = Date.now();
    const entry = detailsRateLimit.get(chatId);

    // If no entry exists or reset time has passed, create/reset entry
    if (!entry || now >= entry.resetTime) {
        detailsRateLimit.set(chatId, {
            count: 1,
            resetTime: now + 5 * 60 * 1000 // 5 minutes from now
        });
        return { allowed: true, remainingUses: 4, resetTime: now + 5 * 60 * 1000 };
    }

    // Check if limit exceeded
    if (entry.count >= 5) {
        return { allowed: false, resetTime: entry.resetTime };
    }

    // Increment count and allow
    entry.count++;
    detailsRateLimit.set(chatId, entry);
    return { allowed: true, remainingUses: 5 - entry.count, resetTime: entry.resetTime };
};


export const groupTypeChangeHandler = async (msg: any) => {
    try {
        if (msg.migrate_from_chat_id && msg.sender_chat.id) {
            const newGroup = { id: msg.sender_chat.id, name: msg.sender_chat.title, link: '', created_at: new Date(), updated_at: new Date() }
            const oldId = msg.migrate_from_chat_id
            await handleGroupTypeChange(oldId, newGroup);
        }
        return;
    }
    catch (error) {
        console.error('Error handling group type change:', error);
    }

}

export const getRandomInt = (min: number = 1, max: number = 10000): number => {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
};


// TODO extend for more info
export const detailsHandler = async (bot: TelegramBot, msg: TelegramBot.Message) => {
    try {
        const chatId = msg.chat.id;

        // Check rate limit
        const rateLimitResult = checkRateLimit(chatId);
        if (!rateLimitResult.allowed) {
            const resetTime = new Date(rateLimitResult.resetTime!);
            const timeUntilReset = Math.ceil((rateLimitResult.resetTime! - Date.now()) / 1000 / 60); // minutes
            await bot.sendMessage(chatId, `⏱️ Rate limit exceeded. You can use this command 5 times per 5 minutes.\n\nTry again in ${timeUntilReset} minute(s).`);
            return;
        }

        let address: string;

        // Check if address is provided in the command
        if (msg.text && msg.text.split(' ').length > 1) {
            address = msg.text.split(' ')[1];
        } else {
            // Try to get address from group config
            const config = await getGroupConfig(BigInt(chatId));
            if (!config || !config.address) {
                await bot.sendMessage(chatId, 'Please provide a token address or register your token first using /start in private chat with bot.\n\nUsage: `/d <token_address>` or `/details <token_address>`', { parse_mode: 'Markdown' });
                return;
            }
            address = config.address;
        }

        logInfo('detailsHandler', `Fetching details for token ${address}`);

        // Show loading message
        const loadingMessage = await bot.sendMessage(chatId, '� Fetching token details...');

        let fetchFunc
        try {
            const bobCanisterId = BigInt(address);
            fetchFunc = fetchBobTokenInfo(bobCanisterId);
        } catch {
            fetchFunc = fetchTokenInfo(address);

        }
        const [tokenInfo, metricsData] = await Promise.all([
            fetchFunc,
            getTokenMetricsFromDB(address)
        ]);
        // Debug removed - issues identified and fixed
        if (!tokenInfo) {
            await bot.editMessageText('❌ Token not found or invalid address.', {
                chat_id: chatId,
                message_id: loadingMessage.message_id
            });
            return;
        }

        if (!metricsData) {
            await bot.editMessageText(`📊 *${tokenInfo.name || 'Unknown'}* (${tokenInfo.symbol || '?'})\n\n❌ Metrics data not available for this token.`, {
                chat_id: chatId,
                message_id: loadingMessage.message_id,
                parse_mode: 'Markdown'
            });
            return;
        }

        // Helper function to format numbers - handles any number size
        const formatNumber = (num: number | string | null | undefined): string => {
            if (num === null || num === undefined) return 'N/A';
            const numValue = typeof num === 'string' ? parseFloat(num) : num;
            if (isNaN(numValue)) return 'N/A';
            if (numValue === 0) return '$0.00';

            const absValue = Math.abs(numValue);
            const sign = numValue < 0 ? '-' : '';

            // Handle large numbers with K/M/B notation
            if (absValue >= 1000000000) return `${sign}$${(absValue / 1000000000).toFixed(2)}B`;
            if (absValue >= 1000000) return `${sign}$${(absValue / 1000000).toFixed(2)}M`;
            if (absValue >= 1000) return `${sign}$${(absValue / 1000).toFixed(2)}K`;

            // For smaller numbers, determine appropriate decimal places
            if (absValue >= 1) return `${sign}$${absValue.toFixed(2)}`;

            // For very small numbers, find the first significant digit
            const scientificStr = absValue.toExponential();
            const exponentMatch = scientificStr.match(/e([+-]\d+)/);
            if (!exponentMatch) return `${sign}$${absValue.toFixed(6)}`;

            const exponent = parseInt(exponentMatch[1]);
            const decimalPlaces = Math.max(2, Math.abs(exponent) + 2);

            // Cap at reasonable decimal places to avoid extremely long strings
            const maxDecimals = Math.min(decimalPlaces, 12);
            return `${sign}$${absValue.toFixed(maxDecimals)}`;
        };

        const formatPrice = (price: number | string | null | undefined): string => {
            if (price === null || price === undefined) return 'N/A';
            const priceValue = typeof price === 'string' ? parseFloat(price) : price;
            if (isNaN(priceValue)) return 'N/A';
            if (priceValue === 0) return '$0.00';

            const absValue = Math.abs(priceValue);
            const sign = priceValue < 0 ? '-' : '';

            // For prices >= $1, show 2 decimal places
            if (absValue >= 1) return `${sign}$${absValue.toFixed(2)}`;

            // For prices >= $0.01, show 4 decimal places
            if (absValue >= 0.01) return `${sign}$${absValue.toFixed(4)}`;

            // For very small prices, use scientific notation to determine decimal places
            const scientificStr = absValue.toExponential();
            const exponentMatch = scientificStr.match(/e([+-]\d+)/);
            if (!exponentMatch) return `${sign}$${absValue.toFixed(6)}`;

            const exponent = parseInt(exponentMatch[1]);

            // Calculate appropriate decimal places based on the exponent
            // For e-6 (0.000001), we want 6+ decimal places
            // For e-9 (0.000000001), we want 9+ decimal places
            const decimalPlaces = Math.max(6, Math.abs(exponent) + 2);

            // Cap at 15 decimal places to avoid floating point precision issues
            const maxDecimals = Math.min(decimalPlaces, 15);
            return `${sign}$${absValue.toFixed(maxDecimals)}`;
        };

        // Helper function to format percentage
        const formatPercentage = (change: number | string | null | undefined): string => {
            if (change === null || change === undefined) return 'N/A';
            const changeValue = typeof change === 'string' ? parseFloat(change) : change;
            if (isNaN(changeValue)) return 'N/A';
            const sign = changeValue >= 0 ? '+' : '';
            return `${sign}${changeValue.toFixed(2)}%`;
        };

        // Calculate price per 1M tokens
        const calculatePricePer1M = (price: number | string | null | undefined): string => {
            if (price === null || price === undefined) return 'N/A';
            const priceValue = typeof price === 'string' ? parseFloat(price) : price;
            if (isNaN(priceValue)) return 'N/A';
            const pricePer1M = priceValue * 1000000;
            return formatNumber(pricePer1M).replace('$', ''); // Remove $ since we'll add it in the message
        };

        // Calculate market cap
        const calculateMarketCap = (price: number | string | null | undefined, totalSupply: string | null | undefined): string => {
            if (price === null || price === undefined || !totalSupply) return 'N/A';
            const priceValue = typeof price === 'string' ? parseFloat(price) : price;
            if (isNaN(priceValue)) return 'N/A';

            // Parse total supply and convert from smallest unit (e8s) to tokens
            const supplyValue = parseFloat(totalSupply);
            if (isNaN(supplyValue)) return 'N/A';

            // Assuming 8 decimals (e8s format), convert to actual token amount
            const tokenSupply = supplyValue / (10 ** 8);
            const marketCap = priceValue * tokenSupply;

            return formatNumber(marketCap).replace('$', ''); // Remove $ since we'll add it in the message
        };

        // Build the message
        const message = `� *Token Details*

*${tokenInfo.name || 'Unknown'}* (${tokenInfo.symbol || '?'})
\`${address}\`

💵 *Price:* ${formatPrice(metricsData.price_usd)}
💰 *Price per 1M tokens:* $${calculatePricePer1M(metricsData.price_usd)}
🏦 *Market Cap:* $${calculateMarketCap(metricsData.price_usd, tokenInfo.totalSupply)}
📈 *24h Change:* ${formatPercentage(metricsData.price_usd_change)}

� *Volume:*
• 24h: ${formatNumber(metricsData.volume_usd_1d)}
• 7d: ${formatNumber(metricsData.volume_usd_7d)}
• Total: ${formatNumber(metricsData.total_volume_usd)}

💰 *Fees (24h):* ${formatNumber(metricsData.fees_usd)}
🔄 *Transactions:* ${metricsData.tx_count || 'N/A'}

🕒 *Last Updated:* ${metricsData.updated_at ? new Date(metricsData.updated_at).toLocaleString() : 'N/A'}

_Remaining uses: ${rateLimitResult.remainingUses}/5 in this 5-minute window_`;

        await bot.editMessageText(message, {
            chat_id: chatId,
            message_id: loadingMessage.message_id,
            parse_mode: 'Markdown'
        });

    } catch (error) {
        logError('detailsHandler', error as Error);

        const errorChatId = msg.chat.id;
        await bot.sendMessage(errorChatId, '❌ An error occurred while fetching the token details. Please try again later.');
    }
};