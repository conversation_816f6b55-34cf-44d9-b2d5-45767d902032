/**
 * Formats a number to show 3 decimal places after the first non-zero digit
 * @param value The number to format
 * @returns Formatted string
 */
export function formatTokenAmount(value: number): string {
    if (value === 0) {
        return '0';
    }

    // For numbers 1 or greater, format with thousands separators and up to 3 decimal places.
    if (value >= 1) {
        return value.toLocaleString('en-US', {
            maximumFractionDigits: 3,
        });
    }

    // For numbers less than 1, apply the special formatting rule.
    // Use `toFixed` with a high precision to get a string representation without scientific notation.
    const valueString = value.toFixed(50);

    // Find the index of the first non-zero digit after the decimal point.
    const decimalIndex = valueString.indexOf('.');
    let firstNonZeroIndex = -1;

    for (let i = decimalIndex + 1; i < valueString.length; i++) {
        if (valueString[i] !== '0') {
            firstNonZeroIndex = i;
            break;
        }
    }

    // If for some reason no non-zero digit is found, return 0.
    if (firstNonZeroIndex === -1) {
        return '0';
    }

    // Calculate the total number of decimal places to show:
    // (number of leading zeros) + (the first non-zero digit) + (3 additional digits).
    const leadingZeros = firstNonZeroIndex - decimalIndex - 1;
    const digitsToShow = leadingZeros + 1 + 3;

    // Use toFixed() again to perform rounding and get the final string.
    return value.toFixed(digitsToShow);
}


/**
 * Formats a dollar amount with commas for thousands and appropriate decimals
 * @param value The dollar amount to format
 * @returns Formatted string
 */
export function formatDollarAmount(value: number, decimals: boolean = true): string {
    if (Number.isInteger(value) || decimals === false) {
        return value.toLocaleString('en-US', { maximumFractionDigits: 0 });
    }
    return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
}
