import { formatTokenAmount, formatDollarAmount } from './numberFormatting';
import { NewPoolMessageData, RobertChoiceMessageData, RobertTokenInfo } from '../types';
import { trendingNumbersMap } from './trending';

// TODO move interfaces
export interface TokenData {
    address: string;
    name: string;
    symbol: string;
    amount: number;
    priceUSD: number;
    pricePairToken: number;
    totalSupply?: number;
}

export interface BuyMessageData {
    spentToken: TokenData;
    gotToken: TokenData;
    pairAddress: string;
    spentDollars: number;
    holderIncrease: string;
    holderWallet: string;
    marketcap: number;
    dex: string;
    icpCollected?: number;
    timestamp?: string;
    holderPercentageOwned?: number;
}

export interface Socials {
    website: string | null;
    telegram: string | null;
    x: string | null;
    openChat: string | null;
}

export interface SubscriberData {
    emoji: string;
    socials: Socials;
}


const getBondingStatus = (icpCollected: number): string => {
    const icpToGather = 500;
    if (icpCollected < 0) {
        return '';
    }

    const percentage = (Math.min(Math.max(icpCollected / icpToGather, 0), 1));
    const greenSquares = Math.round(percentage * 10);
    const redSquares = 10 - greenSquares;

    return `**Bonding progress:**\n` + '🟩'.repeat(greenSquares) + '🟥'.repeat(redSquares) + '\n' + `**${icpCollected.toFixed(2)}/${icpToGather} ICP \\[${(percentage * 100).toFixed(2)}%\]**`;
}

export const getFakeBuyMessage = (gotToken: TokenData, pairAddress: string): BuyMessageData => {
    return {
        gotToken,
        spentToken: {
            address: 'ryjl3-tyaaa-aaaaa-aaaba-cai',
            name: 'Internet Computer',
            symbol: 'ICP',
            amount: 100,
            priceUSD: 10,
            pricePairToken: 0.0001,

        },
        pairAddress: pairAddress,
        spentDollars: 1000,
        holderIncrease: 'New Holder!',
        holderWallet: '5dcn7-ic2md-acdb6-6a4j6-jmw4m-zb75p-5emge-bngjv-x5znn-wfhbl-5ae', //blockchain pill address
        marketcap: 100000000000,
        dex: 'ICPSwap'
    }
}

/**
 * Generates a formatted string of social media links
 * @param socials Object containing social media links
 * @returns Formatted string of social links or empty string if no links are present
 */
function generateSocialLinksString(socials: Socials): string {
    const socialLinks = [];

    // Define the order and display text for each social type
    const socialTypes: Array<{ key: keyof Socials; label: string }> = [
        { key: 'website', label: 'Website' },
        { key: 'telegram', label: 'Telegram' },
        { key: 'x', label: 'X' },
        { key: 'openChat', label: 'OpenChat' }
    ];

    // Add each social link if it exists
    for (const { key, label } of socialTypes) {
        if (socials[key]) {
            socialLinks.push(`[${label}](${socials[key]})`);
        }
    }

    // Return formatted string if there are any links, otherwise empty string
    return socialLinks.length > 0 ? `**👥${socialLinks.join(' | ')}**\n` : '';
}
const getHolderIncreaseText = (increase: string) => {
    if (!increase) return '';
    if (increase.startsWith('New')) return `**👥 ${increase}**\n`;
    if (increase.startsWith('+')) return `**⏫ Position Increase: ${increase}**\n`;
    return '';
};

export function formatBuyMessage(data: BuyMessageData, subscriberData: SubscriberData, rank: number | undefined = undefined): string {
    const dexscreenerLink = `https://dexscreener.com/icp/${data.gotToken.address}`;
    const dextoolsLink = `https://www.dextools.io/app/en/icp/pair-explorer/${data.pairAddress}`;
    //todo make it dynamic based on dex.
    const tokenDetailsLink = `https://app.icpswap.com/info-tokens/details/${data.gotToken.address}`;
    const tokenName = subscriberData.socials.telegram ? `[${data.gotToken.name}](${subscriberData.socials.telegram})` : data.gotToken.name;
    const emojiDenominator = 20
    const emojis = subscriberData.emoji.repeat(Math.min(Math.floor(data.spentDollars) / emojiDenominator | 1, 250));
    const socials = generateSocialLinksString(subscriberData.socials)

    const holderIncrease = getHolderIncreaseText(data.holderIncrease);
    let trending = '';
    if (rank) {
        trending = `\n🚀[ICP Trending ${trendingNumbersMap[rank - 1]}](https://t.me/icp_trending)`;
    }
    let holderPercentageOwned = undefined;
    if (data.holderPercentageOwned) {
        holderPercentageOwned = data.holderPercentageOwned
    }
    const formatHolderPercentage = (percentage: number | undefined): string => {
        if (!percentage) return '';
        if (percentage < 0.01) return '\\[< 0.01%\]';
        return `\\[${percentage.toFixed(2)}%\]`;
    };

    const holderWallet = data.holderWallet ? `**💸[Holder wallet](https://www.icexplorer.io/address/detail/${data.holderWallet}) ${holderPercentageOwned ? formatHolderPercentage(holderPercentageOwned) : ''}**\n` : ''
    let when = '';
    if (data.timestamp) {
        when = `🕐${data.timestamp} UTC\n\n`
    }
    if (data.dex === 'bob.fun') {
        let bondingStatus = '';
        if (data.icpCollected) {
            bondingStatus = getBondingStatus(data.icpCollected)
        }
        // TODO remove duplicates and create one func for all bob and swaps.
        const bobLink = `https://launch.bob.fun/coin/?id=${data.gotToken.address}`
        return [
            `**__🚨 ${tokenName} New Buy!🚨__**\n\n`,
            `${emojis}\n\n`,
            `**💰Spent: ${formatTokenAmount(data.spentToken.amount)} ${data.spentToken.symbol} \\[$${formatDollarAmount(data.spentDollars)}\]**\n`,
            // `**🧳Bought: ${formatTokenAmount(data.gotToken.amount)} $${data.gotToken.symbol}**\n`,
            `**💵Price: $${formatTokenAmount(data.gotToken.priceUSD)}**\n`,
            `**📊Marketcap: $${formatDollarAmount(data.marketcap, false)}**\n`,
            when,
            bondingStatus + "\n\n",
            holderWallet,
            `**📈[bob.fun](${bobLink})**\n`,
            socials,
            trending

        ].join('');
    }

    return [
        `**__🚨 ${tokenName} New Buy!🚨__**\n\n`,
        `${emojis}\n\n`,
        `**💰Spent: ${formatTokenAmount(data.spentToken.amount)} ${data.spentToken.symbol} \\[$${formatDollarAmount(data.spentDollars)}\]**\n`,
        `**🧳Bought: ${formatTokenAmount(data.gotToken.amount)} $${data.gotToken.symbol}**\n`,
        `**💵Price: $${formatTokenAmount(data.gotToken.priceUSD)}**\n`,
        holderIncrease,
        `**📊Marketcap: $${formatDollarAmount(data.marketcap, false)}**\n`,
        when,
        holderWallet,
        `**📈[DexScreener](${dexscreenerLink}) | [DexTools](${dextoolsLink}) | [Swap](${tokenDetailsLink})**\n`,
        socials,
        trending

    ].join('');
}


export const formatNewPoolMessage = (data: NewPoolMessageData): string => {
    let titleStr = '';
    if (data.dex.startsWith('ICPSwap')) {
        // TODO determine properly buys side to always swap from native to token
        titleStr = 'ICPSwap';
        if (data.dex === 'ICPSwap-BOB') {
            titleStr = 'ICPSwap from BOB';
        }
    } else if (data.dex.startsWith('KongSwap')) {
        titleStr = 'KongSwap';
    }
    const dexscreenerLink = `https://dexscreener.com/icp/${data.token0.address}`;
    const dextoolsLink = `https://www.dextools.io/app/en/icp/pair-explorer/${data.canisterId}`;
    //todo make it dynamic based on dex.
    const tokenDetailsLink = `https://app.icpswap.com/info-tokens/details/${data.token0.address}`;
    return [
        `**__🚨 ${titleStr} New Pool found!🚨__**\n\n`,
        `**🏊‍♂️Pair: ${data.token0.symbol}/${data.token1.symbol}**\n`,
        `**📈[DexScreener](${dexscreenerLink}) | [DexTools](${dextoolsLink}) | [ICPSwap](${tokenDetailsLink})**\n`,
    ].join('');
}

// Convert MarkdownV2 to regular Markdown for fallback
export const convertMarkdownV2ToMarkdown = (text: string): string => {
    return text
        .replace(/\\\!/g, '!')  // Unescape exclamation marks
        .replace(/\\\./g, '.')  // Unescape dots
        .replace(/\\\-/g, '-')  // Unescape dashes
        .replace(/\\\+/g, '+')  // Unescape plus signs
        .replace(/\\\=/g, '=')  // Unescape equals signs
        .replace(/\\\|/g, '|')  // Unescape pipes
        .replace(/\\\{/g, '{')  // Unescape braces
        .replace(/\\\}/g, '}')  // Unescape braces
        .replace(/\\\#/g, '#')  // Unescape hash
        .replace(/\\\>/g, '>')  // Unescape greater than
        .replace(/\\\(/g, '(')  // Unescape parentheses
        .replace(/\\\)/g, ')')  // Unescape parentheses
        .replace(/\\\[/g, '[')  // Unescape brackets
        .replace(/\\\]/g, ']')  // Unescape brackets
        .replace(/\\\~/g, '~')  // Unescape tilde
        .replace(/\\\`/g, '`')  // Unescape backticks
        .replace(/\\\*/g, '*')  // Keep asterisks escaped for bold
        .replace(/\\\\_/g, '_'); // Keep underscores escaped for italic
};

// Truncate text to fit within Telegram's limits
const truncateText = (text: string, maxLength: number): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
};

// Clean text data for MarkdownV2 - remove problematic characters
const cleanTextForMarkdown = (text: string): string => {
    if (!text) return '';
    return text
        .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
        .replace(/\s+/g, ' ')  // Normalize whitespace
        .trim();
};


export const formatRobertChoiceMessage = (data: RobertChoiceMessageData): string => {
    console.log('🎨 Formatting Robert choice message with data:', {
        address: data.address,
        name: data.name,
        hasSocialLinks: !!data.socialLinks,
        socialLinks: data.socialLinks,
        hasTokenInfo: !!data.tokenInfo,
        tokenInfoSample: data.tokenInfo ? {
            price_usd: data.tokenInfo.price_usd,
            tx_count: data.tokenInfo.tx_count,
            volume_usd_1d: data.tokenInfo.volume_usd_1d
        } : null
    });

    const bobLink = `https://launch.bob.fun/coin/?id=${data.address}`;

    // Handle date - it might be a Date object or a string from JSON serialization
    const createdDate = (() => {
        const date = typeof data.created_at_token === 'string'
            ? new Date(data.created_at_token)
            : data.created_at_token;

        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    })();

    // Format creator address for display (show first 8 and last 4 characters)
    const formatCreator = (creator: string): string => {
        if (creator.length <= 12) return escapeMarkdownV2(creator);
        const shortAddress = `${creator.slice(0, 8)}...${creator.slice(-4)}`;
        const escapedAddress = escapeMarkdownV2(shortAddress);
        const escapedUrl = escapeMarkdownV2(`https://www.icexplorer.io/address/detail/${creator}`);

        // Calculate creator percentage of total supply (like in transformer.ts)
        let creatorBalancePercentage = '';
        if (data.creatorBalance && data.tokenInfo?.totalSupply) {
            const creatorBalanceFloat = parseFloat(data.creatorBalance) / Math.pow(10, data.tokenInfo.decimals || 8);
            const totalSupplyFloat = parseFloat(data.tokenInfo.totalSupply) / Math.pow(10, data.tokenInfo.decimals || 8);
            const percentage = (creatorBalanceFloat / totalSupplyFloat) * 100;

            if (percentage < 0.01) {
                creatorBalancePercentage = ' \\[<0\\.01%\\]';
            } else {
                creatorBalancePercentage = ` \\[${escapeMarkdownV2(percentage.toFixed(2))}%\\]`;
            }
        }

        return `[${escapedAddress}](${escapedUrl})${creatorBalancePercentage}`;
    };

    // Escape special characters for MarkdownV2 - comprehensive escaping
    const escapeMarkdownV2 = (text: string): string => {
        if (!text) return '';
        return text
            .replace(/\\/g, '\\\\')  // Escape backslashes first
            .replace(/\*/g, '\\*')   // Escape asterisks
            .replace(/_/g, '\\_')    // Escape underscores
            .replace(/\[/g, '\\[')   // Escape square brackets
            .replace(/\]/g, '\\]')   // Escape square brackets
            .replace(/\(/g, '\\(')   // Escape parentheses
            .replace(/\)/g, '\\)')   // Escape parentheses
            .replace(/~/g, '\\~')    // Escape tildes
            .replace(/`/g, '\\`')    // Escape backticks
            .replace(/>/g, '\\>')    // Escape greater than
            .replace(/#/g, '\\#')    // Escape hash
            .replace(/\+/g, '\\+')   // Escape plus
            .replace(/=/g, '\\=')    // Escape equals
            .replace(/\|/g, '\\|')   // Escape pipe
            .replace(/\{/g, '\\{')   // Escape braces
            .replace(/\}/g, '\\}')   // Escape braces
            .replace(/\./g, '\\.')   // Escape dots
            .replace(/!/g, '\\!')    // Escape exclamation marks
            .replace(/-/g, '\\-');   // Escape dashes
    };

    // Format token info section if available
    const formatTokenInfo = (): string => {
        if (!data.tokenInfo) return '';

        const {
            decimals, totalSupply, standard,
            price_usd, price_usd_change, volume_usd_1d,
            total_volume_usd, tx_count
        } = data.tokenInfo;
        console.log("formatTokenInfo", data.tokenInfo)
        const formattedSupply = totalSupply ?
            formatTokenAmount(parseFloat(totalSupply) / Math.pow(10, decimals)) : 'N/A';

        // Calculate market cap if we have price and supply
        const marketCap = (price_usd && totalSupply) ?
            formatDollarAmount(Number(price_usd) * (parseFloat(totalSupply) / Math.pow(10, decimals)), false) : 'N/A';

        const sections = [];

        if (price_usd !== undefined || volume_usd_1d !== undefined) {
            sections.push(`\n**💰 Market Data:**\n`);

            if (price_usd !== undefined) {
                const priceChange = (price_usd_change !== undefined) ?
                    `${Number(price_usd_change) >= 0 ? '🟩 \\+' : '🟥 \\-'} ${escapeMarkdownV2(Number(price_usd_change).toFixed(2))}%` : '';
                sections.push(`**Price:** $${escapeMarkdownV2(formatTokenAmount(Number(price_usd)))} ${priceChange}\n`);
                sections.push(`**Market Cap:** $${escapeMarkdownV2(marketCap)}\n`);
            }

            if (volume_usd_1d !== undefined) {
                sections.push(`**24h Volume:** $${escapeMarkdownV2(formatDollarAmount(Number(volume_usd_1d), false))}\n`);
            }

            if (total_volume_usd !== undefined) {
                sections.push(`**Total Volume:** $${escapeMarkdownV2(formatDollarAmount(Number(total_volume_usd), false))}\n`);
            }

            if (tx_count !== undefined) {
                sections.push(`**Transactions:** ${escapeMarkdownV2(tx_count.toLocaleString())}\n`);
            }
        }

        sections.push(`\n`);
        return sections.join('');
    };
    const tokenInfo = formatTokenInfo();
    console.log("tokenInfo", tokenInfo)

    // Format social links section
    const formatSocialLinks = (): string => {
        console.log('🔗 Formatting social links:', data.socialLinks);
        if (!data.socialLinks) {
            console.log('❌ No social links data available');
            return '';
        }

        const links = [];
        if (data.socialLinks.telegram) {
            links.push(`[Telegram](${escapeMarkdownV2(data.socialLinks.telegram)})`);
        }
        if (data.socialLinks.twitter) {
            links.push(`[Twitter](${escapeMarkdownV2(data.socialLinks.twitter)})`);
        }
        if (data.socialLinks.website) {
            links.push(`[Website](${escapeMarkdownV2(data.socialLinks.website)})`);
        }
        if (data.socialLinks.openChat) {
            links.push(`[OpenChat](${escapeMarkdownV2(data.socialLinks.openChat)})`);
        }

        console.log('🔗 Generated links:', links);
        if (links.length === 0) {
            console.log('❌ No valid social links found');
            return '';
        }

        const result = `**🔗 Links:** ${links.join(' \\| ')}\n\n`;
        console.log('🔗 Final social links section:', result);
        return result;
    };

    // Clean and truncate description to keep message under Telegram's 1024 character limit
    const cleanDescription = cleanTextForMarkdown(data.description);
    const truncatedDescription = truncateText(cleanDescription, 200);

    return [
        `**__🎯 Robert's New Choice\\! 🎯__**\n\n`,
        `**🪙 Token:** \`${data.address}\` ${escapeMarkdownV2(data.name)} \\($${escapeMarkdownV2(data.ticker)}\\)\n\n`,
        `**📝 Description:** ${escapeMarkdownV2(truncatedDescription)}\n`,
        `**👤 Creator:** ${formatCreator(data.created_by)}\n`,
        `**📅 Created:** ${escapeMarkdownV2(createdDate)}\n\n`,
        tokenInfo,
        formatSocialLinks(),
        `_Robert has spoken\\! This is his latest pick\\._`
    ].join('');
}