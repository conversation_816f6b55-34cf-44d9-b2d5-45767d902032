import { KeyboardButtonRequestUser, KeyboardButtonPollType, WebAppInfo } from 'node-telegram-bot-api';


export interface Group {
    id: bigint;
    name: string;
    link: string;
    created_at: Date;
    updated_at: Date;
}

export interface GroupConfig {
    address: string
    minBuyAmount: number;
    buyMessageTemplate: string;
    pools: string[];
    socials: Socials;
}

export interface Socials {
    telegram?: string;
    x?: string;
    website?: string;
    openChat?: string;
}

interface KeyboardButtonRequestChat {
    request_id: number;
    chat_is_channel: boolean;
    chat_is_forum?: boolean | undefined;
    chat_has_username?: boolean | undefined;
    chat_is_created?: boolean | undefined;
    user_administrator_rights?: { can_manage_chat: boolean } | undefined;
    bot_administrator_rights?: boolean | undefined;
    bot_is_member?: boolean | undefined;
}

interface KeyboardButton {
    text: string;
    request_user?: KeyboardButtonRequestUser | undefined;
    request_chat?: KeyboardButtonRequestChat | undefined;
    request_contact?: boolean | undefined;
    request_location?: boolean | undefined;
    request_poll?: KeyboardButtonPollType;
    web_app?: WebAppInfo;
}

export interface CustomKeyboardButton extends KeyboardButton {
    request_chat?: {
        request_id: number;
        chat_is_channel: boolean;
        user_administrator_rights?: {
            can_manage_chat: boolean
        };
        bot_is_member: boolean;
    };
}

export interface BuyMessageData {
    spentToken: {
        address: string;
        name: string;
        symbol: string;
        amount: number;
        priceUSD: number;
        pricePairToken: number;
    };
    gotToken: {
        address: string;
        name: string;
        symbol: string;
        amount: number;
        priceUSD: number;
        pricePairToken: number;
    };
    pairAddress: string;
    spentDollars: number;
    holderIncrease: string;
    holderWallet: string;
    marketcap: number;
    dex: string;
}


export interface NewPoolMessageData {
    canisterId: string;
    token0: TokenData;
    token1: TokenData;
    dex: string;
    type: string;
}

export interface RobertTokenInfo {
    decimals: number;
    totalSupply: string;
    standard: string;
    owner: string;
    poolAddresses: string[];
    // Market metrics from token_metrics table
    price_usd?: number;
    price_usd_change?: number;
    volume_usd_1d?: number;
    volume_usd_7d?: number;
    total_volume_usd?: number;
    fees_usd?: number;
    tx_count?: number;
}

export interface SocialLinks {
    telegram?: string;
    twitter?: string;
    website?: string;
    openChat?: string;
}

export interface RobertChoiceMessageData {
    address: string;
    ticker: string;
    name: string;
    description: string;
    created_at_token: Date | string; // Can be Date object or string from JSON serialization
    created_by: string;
    creatorBalance?: string;
    zero_point_one_icp_tokens_out?: string; // Serialized as string from BigInt
    image: string;
    type: string;
    socialLinks?: SocialLinks; // Social media links from Bob canister
    tokenInfo?: RobertTokenInfo; // Extended token information with market data
}


export interface TokenData {
    address: string;
    name: string;
    symbol: string;
    decimals: number;
    totalSupply: string;
    standard: 'DIP20' | 'ICRC';
    owner: string;
    poolAddresses: string[];
}